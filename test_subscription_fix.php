<?php

require_once __DIR__ . '/vendor/autoload.php';

use App\Models\User;
use Illuminate\Foundation\Application;
use Illuminate\Foundation\Configuration\Exceptions;
use Illuminate\Foundation\Configuration\Middleware;

// Bootstrap Laravel
$app = Application::configure(basePath: __DIR__)
    ->withRouting(
        web: __DIR__.'/routes/web.php',
        api: __DIR__.'/routes/api.php',
        commands: __DIR__.'/routes/console.php',
        health: '/up',
    )
    ->withMiddleware(function (Middleware $middleware) {
        //
    })
    ->withExceptions(function (Exceptions $exceptions) {
        //
    })->create();

$app->make(\Illuminate\Contracts\Console\Kernel::class)->bootstrap();

try {
    // Test finding a user
    $user = User::where('uuid', 'a750bc74-6aef-4e2a-8cce-32b29b1d427d')->first();
    
    if (!$user) {
        echo "User not found\n";
        exit(1);
    }
    
    echo "User found: {$user->email}\n";
    
    // Test getCurrentSubscription method
    $currentSubscription = $user->getCurrentSubscription();
    
    if ($currentSubscription) {
        echo "Current subscription found: {$currentSubscription->id}\n";
        echo "Plan: {$currentSubscription->subscriptionPlan->name}\n";
        echo "Server Pool: {$currentSubscription->serverPool->name}\n";
        echo "Expires at: {$currentSubscription->expires_at}\n";
        echo "Traffic limit: {$currentSubscription->traffic_limit_gb} GB\n";
        echo "Traffic used: {$currentSubscription->traffic_used_gb} GB\n";
    } else {
        echo "No current subscription found\n";
    }
    
    // Test currentSubscription relationship
    $subscriptionRelation = $user->currentSubscription;
    echo "Subscription relation type: " . get_class($subscriptionRelation) . "\n";
    
    // Test accessing subscription through relationship
    $subscriptionFromRelation = $user->currentSubscription()->first();
    if ($subscriptionFromRelation) {
        echo "Subscription from relation: {$subscriptionFromRelation->id}\n";
    } else {
        echo "No subscription from relation\n";
    }
    
    echo "Test completed successfully!\n";
    
} catch (\Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
    echo "File: " . $e->getFile() . ":" . $e->getLine() . "\n";
    exit(1);
}
