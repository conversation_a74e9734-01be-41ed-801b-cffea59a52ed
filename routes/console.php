<?php

use Illuminate\Foundation\Inspiring;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Schedule;

Artisan::command('inspire', function () {
    $this->comment(Inspiring::quote());
})->purpose('Display an inspiring quote');

// Schedule the subscriptions sync command to run every minute
Schedule::command('xui:sync --all')->everyThirtySeconds();
    // ->withoutOverlapping();
// Schedule::command('subscriptions:sync')->everyMinute()
//     ->withoutOverlapping();

// Schedule expired subscriptions processing to run every hour
Schedule::command('subscriptions:expire --force')->everyMinute()
    ->withoutOverlapping()
    ->runInBackground()
    ->onOneServer();

// Schedule::command('server:update-status')->everyThirtySeconds();
