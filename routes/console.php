<?php

use Illuminate\Foundation\Inspiring;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Schedule;

Artisan::command('inspire', function () {
    $this->comment(Inspiring::quote());
})->purpose('Display an inspiring quote');

// Schedule the subscriptions sync command to run every minute
Schedule::command('xui:sync --all')->everyThirtySeconds();
Schedule::command('subscriptions:sync')->everyMinute();
// Schedule::command('server:update-status')->everyThirtySeconds();
