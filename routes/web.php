<?php

use App\Http\Controllers\SubscriptionController;
use App\Http\Controllers\SupportController;
use Illuminate\Support\Facades\Route;

Route::get('/', function () {
    return view('welcome');
});

// Payment result pages
Route::get('/payment/success/{orderId}', [App\Http\Controllers\PaymentController::class, 'paymentSuccess'])
    ->name('payment.success');
Route::get('/payment/fail/{orderId}', [App\Http\Controllers\PaymentController::class, 'paymentFail'])
    ->name('payment.fail');

// Admin panel
Route::get('/admin/{path?}', [App\Http\Controllers\AdminController::class, 'index'])
    ->where('path', '.*')
    ->name('admin');

// Test QR code page
Route::get('/test-qr', function () {
    return view('test-qr');
});

// Test accordion page
Route::get('/test-accordion', function () {
    return view('test-accordion');
});

// Subscription endpoints with middleware
Route::middleware(['App\Http\Middleware\SubscriptionMiddleware'])->group(function () {
    // Main subscription endpoint
    Route::get('/subs/{uuid}', [SubscriptionController::class, 'show'])
        ->where('uuid', '[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}');

    // Modern subscription endpoint
    Route::get('/subs/{uuid}/modern', [SubscriptionController::class, 'showModern'])
        ->where('uuid', '[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}')
        ->name('subscription.modern');

    // Modern subscription endpoint without QR
    Route::get('/subs/{uuid}/modern-no-qr', [SubscriptionController::class, 'showModernNoQR'])
        ->where('uuid', '[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}');

    // User statistics endpoint (for debugging/monitoring)
    Route::get('/subs/{uuid}/stats', [SubscriptionController::class, 'stats'])
        ->where('uuid', '[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}');

    // Force refresh subscription content
    Route::post('/subs/{uuid}/refresh', [SubscriptionController::class, 'refresh'])
        ->where('uuid', '[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}');

    // Update routing preference
    Route::post('/subs/{uuid}/routing-preference', [SubscriptionController::class, 'updateRoutingPreference'])
        ->where('uuid', '[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}');

    // Update user traffic statistics
    Route::post('/subs/{uuid}/update-traffic', [SubscriptionController::class, 'updateUserTraffic'])
        ->where('uuid', '[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}');

    // Renew subscription endpoint (show renewal page)
    Route::get('/subs/{uuid}/renew', [SubscriptionController::class, 'showRenewal'])
        ->where('uuid', '[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}')
        ->name('subscription.renew');

    // Process renewal/purchase
    Route::post('/subs/{uuid}/renew', [SubscriptionController::class, 'processRenewal'])
        ->where('uuid', '[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}')
        ->name('subscription.process_renewal');
});

// Support routes
Route::get('/support/{uuid}', [SupportController::class, 'index'])
    ->where('uuid', '[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}')
    ->name('support.index');

Route::post('/support/rating', [SupportController::class, 'submitRating'])->name('support.rating');
Route::get('/support/stats', [SupportController::class, 'getRatingStats'])->name('support.stats');
