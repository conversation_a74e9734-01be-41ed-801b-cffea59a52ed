- ## Prologue
    - [Release Notes](/docs/{{version}}/releases)
    - [Upgrade Guide](/docs/{{version}}/upgrade)
    - [Contribution Guide](/docs/{{version}}/contributions)
- ## Getting Started
    - [Installation](/docs/{{version}}/installation)
    - [Configuration](/docs/{{version}}/configuration)
    - [Directory Structure](/docs/{{version}}/structure)
    - [Frontend](/docs/{{version}}/frontend)
    - [Starter Kits](/docs/{{version}}/starter-kits)
    - [Deployment](/docs/{{version}}/deployment)
- ## Architecture Concepts
    - [Request Lifecycle](/docs/{{version}}/lifecycle)
    - [Service Container](/docs/{{version}}/container)
    - [Service Providers](/docs/{{version}}/providers)
    - [Facades](/docs/{{version}}/facades)
- ## The Basics
    - [Routing](/docs/{{version}}/routing)
    - [Middleware](/docs/{{version}}/middleware)
    - [CSRF Protection](/docs/{{version}}/csrf)
    - [Controllers](/docs/{{version}}/controllers)
    - [Requests](/docs/{{version}}/requests)
    - [Responses](/docs/{{version}}/responses)
    - [Views](/docs/{{version}}/views)
    - [Blade Templates](/docs/{{version}}/blade)
    - [Asset Bundling](/docs/{{version}}/vite)
    - [URL Generation](/docs/{{version}}/urls)
    - [Session](/docs/{{version}}/session)
    - [Validation](/docs/{{version}}/validation)
    - [Error Handling](/docs/{{version}}/errors)
    - [Logging](/docs/{{version}}/logging)
- ## Digging Deeper
    - [Artisan Console](/docs/{{version}}/artisan)
    - [Broadcasting](/docs/{{version}}/broadcasting)
    - [Cache](/docs/{{version}}/cache)
    - [Collections](/docs/{{version}}/collections)
    - [Concurrency](/docs/{{version}}/concurrency)
    - [Context](/docs/{{version}}/context)
    - [Contracts](/docs/{{version}}/contracts)
    - [Events](/docs/{{version}}/events)
    - [File Storage](/docs/{{version}}/filesystem)
    - [Helpers](/docs/{{version}}/helpers)
    - [HTTP Client](/docs/{{version}}/http-client)
    - [Localization](/docs/{{version}}/localization)
    - [Mail](/docs/{{version}}/mail)
    - [Notifications](/docs/{{version}}/notifications)
    - [Package Development](/docs/{{version}}/packages)
    - [Processes](/docs/{{version}}/processes)
    - [Queues](/docs/{{version}}/queues)
    - [Rate Limiting](/docs/{{version}}/rate-limiting)
    - [Strings](/docs/{{version}}/strings)
    - [Task Scheduling](/docs/{{version}}/scheduling)
- ## Security
    - [Authentication](/docs/{{version}}/authentication)
    - [Authorization](/docs/{{version}}/authorization)
    - [Email Verification](/docs/{{version}}/verification)
    - [Encryption](/docs/{{version}}/encryption)
    - [Hashing](/docs/{{version}}/hashing)
    - [Password Reset](/docs/{{version}}/passwords)
- ## Database
    - [Getting Started](/docs/{{version}}/database)
    - [Query Builder](/docs/{{version}}/queries)
    - [Pagination](/docs/{{version}}/pagination)
    - [Migrations](/docs/{{version}}/migrations)
    - [Seeding](/docs/{{version}}/seeding)
    - [Redis](/docs/{{version}}/redis)
    - [MongoDB](/docs/{{version}}/mongodb)
- ## Eloquent ORM
    - [Getting Started](/docs/{{version}}/eloquent)
    - [Relationships](/docs/{{version}}/eloquent-relationships)
    - [Collections](/docs/{{version}}/eloquent-collections)
    - [Mutators / Casts](/docs/{{version}}/eloquent-mutators)
    - [API Resources](/docs/{{version}}/eloquent-resources)
    - [Serialization](/docs/{{version}}/eloquent-serialization)
    - [Factories](/docs/{{version}}/eloquent-factories)
- ## Testing
    - [Getting Started](/docs/{{version}}/testing)
    - [HTTP Tests](/docs/{{version}}/http-tests)
    - [Console Tests](/docs/{{version}}/console-tests)
    - [Browser Tests](/docs/{{version}}/dusk)
    - [Database](/docs/{{version}}/database-testing)
    - [Mocking](/docs/{{version}}/mocking)
- ## Packages
    - [Cashier (Stripe)](/docs/{{version}}/billing)
    - [Cashier (Paddle)](/docs/{{version}}/cashier-paddle)
    - [Dusk](/docs/{{version}}/dusk)
    - [Envoy](/docs/{{version}}/envoy)
    - [Fortify](/docs/{{version}}/fortify)
    - [Folio](/docs/{{version}}/folio)
    - [Homestead](/docs/{{version}}/homestead)
    - [Horizon](/docs/{{version}}/horizon)
    - [Mix](/docs/{{version}}/mix)
    - [Octane](/docs/{{version}}/octane)
    - [Passport](/docs/{{version}}/passport)
    - [Pennant](/docs/{{version}}/pennant)
    - [Pint](/docs/{{version}}/pint)
    - [Precognition](/docs/{{version}}/precognition)
    - [Prompts](/docs/{{version}}/prompts)
    - [Pulse](/docs/{{version}}/pulse)
    - [Reverb](/docs/{{version}}/reverb)
    - [Sail](/docs/{{version}}/sail)
    - [Sanctum](/docs/{{version}}/sanctum)
    - [Scout](/docs/{{version}}/scout)
    - [Socialite](/docs/{{version}}/socialite)
    - [Telescope](/docs/{{version}}/telescope)
    - [Valet](/docs/{{version}}/valet)
- [API Documentation](https://api.laravel.com/docs/12.x)
