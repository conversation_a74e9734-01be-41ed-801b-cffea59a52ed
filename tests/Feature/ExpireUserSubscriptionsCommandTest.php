<?php

namespace Tests\Feature;

use App\Jobs\ExpireUserSubscriptionsJob;
use App\Models\User;
use App\Models\UserSubscription;
use App\Models\SubscriptionPlan;
use Carbon\Carbon;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Queue;
use Tests\TestCase;

class ExpireUserSubscriptionsCommandTest extends TestCase
{
    use RefreshDatabase;

    public function test_command_shows_no_expired_subscriptions_message()
    {
        // Create active subscription
        $user = User::factory()->create();
        $plan = SubscriptionPlan::factory()->create();
        
        UserSubscription::factory()->create([
            'user_id' => $user->id,
            'subscription_plan_id' => $plan->id,
            'is_active' => true,
            'expires_at' => Carbon::now()->addDays(30),
        ]);

        $this->artisan('subscriptions:expire')
            ->expectsOutput('✅ No expired subscriptions found.')
            ->assertExitCode(0);
    }

    public function test_command_shows_expired_subscriptions_in_dry_run_mode()
    {
        Queue::fake();

        $user = User::factory()->create([
            'name' => 'Test User',
            'email' => '<EMAIL>',
        ]);
        $plan = SubscriptionPlan::factory()->create(['name' => 'Test Plan']);
        
        UserSubscription::factory()->create([
            'user_id' => $user->id,
            'subscription_plan_id' => $plan->id,
            'is_active' => true,
            'expires_at' => Carbon::now()->subDays(1),
        ]);

        $this->artisan('subscriptions:expire --dry-run')
            ->expectsOutput('📊 Found 1 expired subscription(s):')
            ->expectsOutput('🔍 DRY RUN MODE: No subscriptions will be actually expired.')
            ->assertExitCode(0);

        // Assert no job was dispatched in dry-run mode
        Queue::assertNotPushed(ExpireUserSubscriptionsJob::class);
    }

    public function test_command_dispatches_job_with_force_flag()
    {
        Queue::fake();

        $user = User::factory()->create();
        $plan = SubscriptionPlan::factory()->create();
        
        UserSubscription::factory()->create([
            'user_id' => $user->id,
            'subscription_plan_id' => $plan->id,
            'is_active' => true,
            'expires_at' => Carbon::now()->subDays(1),
        ]);

        $this->artisan('subscriptions:expire --force')
            ->expectsOutput('🚀 Dispatching expiration job...')
            ->expectsOutput('✅ Expiration job has been dispatched successfully!')
            ->assertExitCode(0);

        Queue::assertPushed(ExpireUserSubscriptionsJob::class);
    }

    public function test_command_requires_confirmation_for_normal_execution()
    {
        Queue::fake();

        $user = User::factory()->create();
        $plan = SubscriptionPlan::factory()->create();
        
        UserSubscription::factory()->create([
            'user_id' => $user->id,
            'subscription_plan_id' => $plan->id,
            'is_active' => true,
            'expires_at' => Carbon::now()->subDays(1),
        ]);

        // Test cancellation
        $this->artisan('subscriptions:expire')
            ->expectsQuestion('Are you sure you want to expire 1 subscription(s)?', false)
            ->expectsOutput('❌ Operation cancelled.')
            ->assertExitCode(0);

        Queue::assertNotPushed(ExpireUserSubscriptionsJob::class);

        // Test confirmation
        $this->artisan('subscriptions:expire')
            ->expectsQuestion('Are you sure you want to expire 1 subscription(s)?', true)
            ->expectsOutput('✅ Expiration job has been dispatched successfully!')
            ->assertExitCode(0);

        Queue::assertPushed(ExpireUserSubscriptionsJob::class);
    }

    public function test_command_shows_safety_warning_for_large_numbers()
    {
        Queue::fake();

        $plan = SubscriptionPlan::factory()->create();
        
        // Create 51 expired subscriptions (more than safety limit of 50)
        for ($i = 0; $i < 51; $i++) {
            $user = User::factory()->create();
            UserSubscription::factory()->create([
                'user_id' => $user->id,
                'subscription_plan_id' => $plan->id,
                'is_active' => true,
                'expires_at' => Carbon::now()->subDays(1),
            ]);
        }

        $this->artisan('subscriptions:expire')
            ->expectsOutput('⚠️  Found 51 expired subscriptions, which is quite a lot!')
            ->expectsOutput('💡 Use --force flag if you\'re sure you want to expire all these subscriptions.')
            ->assertExitCode(1);

        Queue::assertNotPushed(ExpireUserSubscriptionsJob::class);
    }

    public function test_command_bypasses_safety_check_with_force_flag()
    {
        Queue::fake();

        $plan = SubscriptionPlan::factory()->create();
        
        // Create 51 expired subscriptions
        for ($i = 0; $i < 51; $i++) {
            $user = User::factory()->create();
            UserSubscription::factory()->create([
                'user_id' => $user->id,
                'subscription_plan_id' => $plan->id,
                'is_active' => true,
                'expires_at' => Carbon::now()->subDays(1),
            ]);
        }

        $this->artisan('subscriptions:expire --force')
            ->expectsOutput('✅ Expiration job has been dispatched successfully!')
            ->assertExitCode(0);

        Queue::assertPushed(ExpireUserSubscriptionsJob::class);
    }

    public function test_command_shows_subscription_details_in_table()
    {
        $user = User::factory()->create([
            'name' => 'John Doe',
            'email' => '<EMAIL>',
        ]);
        $plan = SubscriptionPlan::factory()->create(['name' => 'Premium Plan']);
        
        UserSubscription::factory()->create([
            'user_id' => $user->id,
            'subscription_plan_id' => $plan->id,
            'is_active' => true,
            'expires_at' => Carbon::now()->subDay(),
        ]);

        $this->artisan('subscriptions:expire --dry-run')
            ->expectsOutput('📊 Found 1 expired subscription(s):')
            ->expectsOutputToContain('John Doe')
            ->expectsOutputToContain('<EMAIL>')
            ->expectsOutputToContain('Premium Plan')
            ->assertExitCode(0);
    }
}
