<?php

namespace Tests\Feature;

use App\Events\UserSubscriptionExpired;
use App\Jobs\ExpireUserSubscriptionsJob;
use App\Models\User;
use App\Models\UserSubscription;
use App\Models\SubscriptionPlan;
use App\Models\ServerPool;
use Carbon\Carbon;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Event;
use Illuminate\Support\Facades\Queue;
use Tests\TestCase;

class ExpireUserSubscriptionsJobTest extends TestCase
{
    use RefreshDatabase;

    public function test_job_expires_subscriptions_that_have_passed_expiration_date()
    {
        Event::fake();

        // Create a server pool
        $serverPool = ServerPool::factory()->create();

        // Create a subscription plan
        $plan = SubscriptionPlan::factory()->create();

        // Create users with expired subscriptions
        $expiredUser1 = User::factory()->create([
            'expired' => false,
            'server_pool_id' => $serverPool->id,
        ]);
        $expiredUser2 = User::factory()->create([
            'expired' => false,
            'server_pool_id' => $serverPool->id,
        ]);

        // Create user with active subscription (not expired)
        $activeUser = User::factory()->create([
            'expired' => false,
            'server_pool_id' => $serverPool->id,
        ]);

        // Create expired subscriptions
        $expiredSubscription1 = UserSubscription::factory()->create([
            'user_id' => $expiredUser1->id,
            'subscription_plan_id' => $plan->id,
            'is_active' => true,
            'expires_at' => Carbon::now()->subDays(1), // Expired yesterday
        ]);

        $expiredSubscription2 = UserSubscription::factory()->create([
            'user_id' => $expiredUser2->id,
            'subscription_plan_id' => $plan->id,
            'is_active' => true,
            'expires_at' => Carbon::now()->subHours(1), // Expired 1 hour ago
        ]);

        // Create active subscription
        $activeSubscription = UserSubscription::factory()->create([
            'user_id' => $activeUser->id,
            'subscription_plan_id' => $plan->id,
            'is_active' => true,
            'expires_at' => Carbon::now()->addDays(30), // Expires in 30 days
        ]);

        // Execute the job
        $job = new ExpireUserSubscriptionsJob();
        $job->handle();

        // Refresh models from database
        $expiredUser1->refresh();
        $expiredUser2->refresh();
        $activeUser->refresh();
        $expiredSubscription1->refresh();
        $expiredSubscription2->refresh();
        $activeSubscription->refresh();

        // Assert expired subscriptions are deactivated
        $this->assertFalse($expiredSubscription1->is_active);
        $this->assertNotNull($expiredSubscription1->deactivated_at);
        $this->assertFalse($expiredSubscription2->is_active);
        $this->assertNotNull($expiredSubscription2->deactivated_at);

        // Assert active subscription remains active
        $this->assertTrue($activeSubscription->is_active);
        $this->assertNull($activeSubscription->deactivated_at);

        // Assert users are marked as expired and removed from server pool
        $this->assertTrue($expiredUser1->expired);
        $this->assertNull($expiredUser1->server_pool_id);
        $this->assertTrue($expiredUser2->expired);
        $this->assertNull($expiredUser2->server_pool_id);

        // Assert active user remains active
        $this->assertFalse($activeUser->expired);
        $this->assertEquals($serverPool->id, $activeUser->server_pool_id);

        // Assert events were dispatched
        Event::assertDispatched(UserSubscriptionExpired::class, 2);
        Event::assertDispatched(UserSubscriptionExpired::class, function ($event) use ($expiredUser1) {
            return $event->user->id === $expiredUser1->id;
        });
        Event::assertDispatched(UserSubscriptionExpired::class, function ($event) use ($expiredUser2) {
            return $event->user->id === $expiredUser2->id;
        });
    }

    public function test_job_handles_no_expired_subscriptions_gracefully()
    {
        Event::fake();

        // Create a user with active subscription
        $user = User::factory()->create();
        $plan = SubscriptionPlan::factory()->create();
        
        UserSubscription::factory()->create([
            'user_id' => $user->id,
            'subscription_plan_id' => $plan->id,
            'is_active' => true,
            'expires_at' => Carbon::now()->addDays(30),
        ]);

        // Execute the job
        $job = new ExpireUserSubscriptionsJob();
        $job->handle();

        // Assert no events were dispatched
        Event::assertNotDispatched(UserSubscriptionExpired::class);
    }

    public function test_job_handles_subscription_without_user()
    {
        Event::fake();

        $plan = SubscriptionPlan::factory()->create();

        // Create subscription with non-existent user
        UserSubscription::factory()->create([
            'user_id' => 99999, // Non-existent user ID
            'subscription_plan_id' => $plan->id,
            'is_active' => true,
            'expires_at' => Carbon::now()->subDays(1),
        ]);

        // Execute the job - should not throw exception
        $job = new ExpireUserSubscriptionsJob();
        $job->handle();

        // Assert no events were dispatched
        Event::assertNotDispatched(UserSubscriptionExpired::class);
    }

    public function test_job_can_be_dispatched()
    {
        Queue::fake();

        ExpireUserSubscriptionsJob::dispatch();

        Queue::assertPushed(ExpireUserSubscriptionsJob::class);
    }

    public function test_job_logs_processing_information()
    {
        // This test would require log testing setup
        // For now, we'll just ensure the job runs without errors
        $job = new ExpireUserSubscriptionsJob();
        $job->handle();

        $this->assertTrue(true); // Job completed without exceptions
    }
}
