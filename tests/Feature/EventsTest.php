<?php

namespace Tests\Feature;

use App\Events\UserCreated;
use App\Events\UserSubscriptionRenewed;
use App\Events\UserSubscriptionExpired;
use App\Events\PaymentReceived;
use App\Events\ReferralRewardEarned;
use App\Models\User;
use App\Models\UserSubscription;
use App\Models\SubscriptionPlan;
use App\Models\Payment;
use App\Models\Order;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Event;
use Tests\TestCase;

class EventsTest extends TestCase
{
    use RefreshDatabase;

    public function test_user_created_event_is_dispatched_and_handled()
    {
        Event::fake();

        $user = User::factory()->create();

        UserCreated::dispatch($user);

        Event::assertDispatched(UserCreated::class, function ($event) use ($user) {
            return $event->user->id === $user->id;
        });
    }

    public function test_user_subscription_renewed_event_is_dispatched_and_handled()
    {
        Event::fake();

        $user = User::factory()->create();
        $plan = SubscriptionPlan::factory()->create();
        $subscription = UserSubscription::factory()->create([
            'user_id' => $user->id,
            'subscription_plan_id' => $plan->id,
        ]);

        UserSubscriptionRenewed::dispatch($user, $subscription);

        Event::assertDispatched(UserSubscriptionRenewed::class, function ($event) use ($user, $subscription) {
            return $event->user->id === $user->id && $event->subscription->id === $subscription->id;
        });
    }

    public function test_user_subscription_expired_event_is_dispatched_and_handled()
    {
        Event::fake();

        $user = User::factory()->create();

        UserSubscriptionExpired::dispatch($user);

        Event::assertDispatched(UserSubscriptionExpired::class, function ($event) use ($user) {
            return $event->user->id === $user->id;
        });
    }

    public function test_payment_received_event_is_dispatched_and_handled()
    {
        Event::fake();

        $user = User::factory()->create();
        $plan = SubscriptionPlan::factory()->create();
        $order = Order::factory()->create([
            'user_id' => $user->id,
            'subscription_plan_id' => $plan->id,
        ]);
        $payment = Payment::factory()->create([
            'order_id' => $order->id,
        ]);

        PaymentReceived::dispatch($payment, $order);

        Event::assertDispatched(PaymentReceived::class, function ($event) use ($payment, $order) {
            return $event->payment->id === $payment->id && $event->order->id === $order->id;
        });
    }

    public function test_referral_reward_earned_event_is_dispatched_and_handled()
    {
        Event::fake();

        $referrer = User::factory()->create();
        $referred = User::factory()->create();
        $payment = Payment::factory()->create();
        $rewardAmount = 100.0;

        ReferralRewardEarned::dispatch($referrer, $referred, $payment, $rewardAmount);

        Event::assertDispatched(ReferralRewardEarned::class, function ($event) use ($referrer, $referred, $payment, $rewardAmount) {
            return $event->referrer->id === $referrer->id
                && $event->referred->id === $referred->id
                && $event->payment->id === $payment->id
                && $event->rewardAmount === $rewardAmount;
        });
    }

    public function test_event_listeners_are_automatically_discovered()
    {
        // Test that listeners are automatically discovered by Laravel
        $listeners = app('events')->getListeners(UserCreated::class);

        $this->assertNotEmpty($listeners);

        // Check that our listener is registered
        $listenerFound = false;
        foreach ($listeners as $listener) {
            if (is_string($listener) && str_contains($listener, 'CreateXuiClientsListener')) {
                $listenerFound = true;
                break;
            }
        }

        $this->assertTrue($listenerFound, 'CreateXuiClientsListener should be automatically discovered');
    }

    public function test_event_tags_are_properly_set()
    {
        $user = User::factory()->create();
        $event = new UserCreated($user);

        $tags = $event->tags();

        $this->assertContains('user:' . $user->id, $tags);
        $this->assertContains('user-created', $tags);
    }

    public function test_conditional_event_dispatch()
    {
        Event::fake();

        $user = User::factory()->create();

        // Test dispatchIf with true condition
        UserCreated::dispatchIf(true, $user);
        Event::assertDispatched(UserCreated::class);

        Event::fake(); // Reset

        // Test dispatchIf with false condition
        UserCreated::dispatchIf(false, $user);
        Event::assertNotDispatched(UserCreated::class);
    }
}
