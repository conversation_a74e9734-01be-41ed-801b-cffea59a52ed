<?php

namespace Tests\Feature;

use App\Events\UserCreated;
use App\Events\PaymentReceived;
use App\Events\UserSubscriptionRenewed;
use App\Events\UserSubscriptionExpired;
use App\Events\ReferralRewardEarned;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Event;
use Tests\TestCase;

class EventListenerDuplicationTest extends TestCase
{
    use RefreshDatabase;

    public function test_user_created_event_has_no_duplicate_listeners()
    {
        $listeners = Event::getListeners(UserCreated::class);
        
        // Count listeners by class name to detect duplicates
        $listenerCounts = [];
        foreach ($listeners as $listener) {
            $listenerKey = $this->getListenerKey($listener);
            $listenerCounts[$listenerKey] = ($listenerCounts[$listenerKey] ?? 0) + 1;
        }
        
        // Check for duplicates
        foreach ($listenerCounts as $listenerKey => $count) {
            $this->assertEquals(1, $count, "Listener {$listenerKey} is registered {$count} times for UserCreated event");
        }
        
        // Ensure we have at least one listener
        $this->assertNotEmpty($listeners, 'UserCreated event should have at least one listener');
    }

    public function test_payment_received_event_has_no_duplicate_listeners()
    {
        $listeners = Event::getListeners(PaymentReceived::class);
        
        // Count listeners by class name to detect duplicates
        $listenerCounts = [];
        foreach ($listeners as $listener) {
            $listenerKey = $this->getListenerKey($listener);
            $listenerCounts[$listenerKey] = ($listenerCounts[$listenerKey] ?? 0) + 1;
        }
        
        // Check for duplicates
        foreach ($listenerCounts as $listenerKey => $count) {
            $this->assertEquals(1, $count, "Listener {$listenerKey} is registered {$count} times for PaymentReceived event");
        }
        
        // Ensure we have at least one listener
        $this->assertNotEmpty($listeners, 'PaymentReceived event should have at least one listener');
    }

    public function test_user_subscription_renewed_event_has_no_duplicate_listeners()
    {
        $listeners = Event::getListeners(UserSubscriptionRenewed::class);
        
        // Count listeners by class name to detect duplicates
        $listenerCounts = [];
        foreach ($listeners as $listener) {
            $listenerKey = $this->getListenerKey($listener);
            $listenerCounts[$listenerKey] = ($listenerCounts[$listenerKey] ?? 0) + 1;
        }
        
        // Check for duplicates
        foreach ($listenerCounts as $listenerKey => $count) {
            $this->assertEquals(1, $count, "Listener {$listenerKey} is registered {$count} times for UserSubscriptionRenewed event");
        }
        
        // Ensure we have at least one listener
        $this->assertNotEmpty($listeners, 'UserSubscriptionRenewed event should have at least one listener');
    }

    public function test_user_subscription_expired_event_has_no_duplicate_listeners()
    {
        $listeners = Event::getListeners(UserSubscriptionExpired::class);
        
        // Count listeners by class name to detect duplicates
        $listenerCounts = [];
        foreach ($listeners as $listener) {
            $listenerKey = $this->getListenerKey($listener);
            $listenerCounts[$listenerKey] = ($listenerCounts[$listenerKey] ?? 0) + 1;
        }
        
        // Check for duplicates
        foreach ($listenerCounts as $listenerKey => $count) {
            $this->assertEquals(1, $count, "Listener {$listenerKey} is registered {$count} times for UserSubscriptionExpired event");
        }
        
        // Ensure we have at least one listener
        $this->assertNotEmpty($listeners, 'UserSubscriptionExpired event should have at least one listener');
    }

    public function test_referral_reward_earned_event_has_no_duplicate_listeners()
    {
        $listeners = Event::getListeners(ReferralRewardEarned::class);
        
        // Count listeners by class name to detect duplicates
        $listenerCounts = [];
        foreach ($listeners as $listener) {
            $listenerKey = $this->getListenerKey($listener);
            $listenerCounts[$listenerKey] = ($listenerCounts[$listenerKey] ?? 0) + 1;
        }
        
        // Check for duplicates
        foreach ($listenerCounts as $listenerKey => $count) {
            $this->assertEquals(1, $count, "Listener {$listenerKey} is registered {$count} times for ReferralRewardEarned event");
        }
        
        // Ensure we have at least one listener
        $this->assertNotEmpty($listeners, 'ReferralRewardEarned event should have at least one listener');
    }

    /**
     * Get a unique key for a listener to identify duplicates.
     */
    private function getListenerKey($listener): string
    {
        if (is_string($listener)) {
            return $listener;
        }
        
        if (is_array($listener) && count($listener) >= 2) {
            $class = is_object($listener[0]) ? get_class($listener[0]) : $listener[0];
            $method = $listener[1] ?? 'handle';
            return "{$class}@{$method}";
        }
        
        if (is_callable($listener)) {
            return 'Closure_' . spl_object_hash($listener);
        }
        
        return 'Unknown_' . serialize($listener);
    }
}
