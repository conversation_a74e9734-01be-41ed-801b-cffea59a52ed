<?php

namespace Tests\Feature;

use App\Events\UserCreated;
use App\Events\UserSubscriptionRenewed;
use App\Events\UserSubscriptionExpired;
use App\Events\PaymentReceived;
use App\Events\ReferralRewardEarned;
use App\Listeners\UserEventSubscriber;
use App\Listeners\PaymentEventSubscriber;
use App\Models\User;
use App\Models\UserSubscription;
use App\Models\SubscriptionPlan;
use App\Models\Payment;
use App\Models\Order;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Event;
use Illuminate\Support\Facades\Log;
use Tests\TestCase;

class EventSubscribersTest extends TestCase
{
    use RefreshDatabase;

    public function test_user_event_subscriber_is_registered()
    {
        $subscribers = app('events')->getSubscribers();
        
        $subscriberFound = false;
        foreach ($subscribers as $subscriber) {
            if ($subscriber instanceof UserEventSubscriber) {
                $subscriberFound = true;
                break;
            }
        }
        
        $this->assertTrue($subscriberFound, 'UserEventSubscriber should be registered');
    }

    public function test_payment_event_subscriber_is_registered()
    {
        $subscribers = app('events')->getSubscribers();
        
        $subscriberFound = false;
        foreach ($subscribers as $subscriber) {
            if ($subscriber instanceof PaymentEventSubscriber) {
                $subscriberFound = true;
                break;
            }
        }
        
        $this->assertTrue($subscriberFound, 'PaymentEventSubscriber should be registered');
    }

    public function test_user_event_subscriber_handles_user_created()
    {
        Log::shouldReceive('info')
            ->once()
            ->with('UserEventSubscriber: User created event received', \Mockery::type('array'));

        $user = User::factory()->create();
        $subscriber = new UserEventSubscriber();
        
        $event = new UserCreated($user);
        $subscriber->handleUserCreated($event);
    }

    public function test_user_event_subscriber_handles_subscription_renewed()
    {
        Log::shouldReceive('info')
            ->once()
            ->with('UserEventSubscriber: User subscription renewed event received', \Mockery::type('array'));

        $user = User::factory()->create();
        $plan = SubscriptionPlan::factory()->create();
        $subscription = UserSubscription::factory()->create([
            'user_id' => $user->id,
            'subscription_plan_id' => $plan->id,
        ]);
        
        $subscriber = new UserEventSubscriber();
        $event = new UserSubscriptionRenewed($user, $subscription);
        
        $subscriber->handleUserSubscriptionRenewed($event);
    }

    public function test_user_event_subscriber_handles_subscription_expired()
    {
        Log::shouldReceive('info')
            ->once()
            ->with('UserEventSubscriber: User subscription expired event received', \Mockery::type('array'));

        $user = User::factory()->create();
        $subscriber = new UserEventSubscriber();
        
        $event = new UserSubscriptionExpired($user);
        $subscriber->handleUserSubscriptionExpired($event);
    }

    public function test_payment_event_subscriber_handles_payment_received()
    {
        Log::shouldReceive('info')
            ->once()
            ->with('PaymentEventSubscriber: Payment received event received', \Mockery::type('array'));

        $user = User::factory()->create();
        $plan = SubscriptionPlan::factory()->create();
        $order = Order::factory()->create([
            'user_id' => $user->id,
            'subscription_plan_id' => $plan->id,
        ]);
        $payment = Payment::factory()->create([
            'order_id' => $order->id,
        ]);
        
        $subscriber = new PaymentEventSubscriber();
        $event = new PaymentReceived($payment, $order);
        
        $subscriber->handlePaymentReceived($event);
    }

    public function test_payment_event_subscriber_handles_referral_reward_earned()
    {
        Log::shouldReceive('info')
            ->once()
            ->with('PaymentEventSubscriber: Referral reward earned event received', \Mockery::type('array'));

        $referrer = User::factory()->create();
        $referred = User::factory()->create();
        $payment = Payment::factory()->create();
        $rewardAmount = 100.0;
        
        $subscriber = new PaymentEventSubscriber();
        $event = new ReferralRewardEarned($referrer, $referred, $payment, $rewardAmount);
        
        $subscriber->handleReferralRewardEarned($event);
    }
}
