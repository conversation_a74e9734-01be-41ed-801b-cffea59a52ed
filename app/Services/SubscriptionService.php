<?php

namespace App\Services;

use App\DTOs\SubscriptionDto;
use App\Models\User;
use App\Models\XuiServer;
use App\Models\ServerPool;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;

class SubscriptionService
{
    public function __construct(
        private XuiManagementService $xuiManagementService
    ) {}

    /**
     * Get subscription content for a user by UUID.
     * Generates VLESS keys on-the-fly from active inbounds.
     */
    public function getSubscriptionByUuid(string $uuid): ?SubscriptionDto
    {
        $user = User::findByUuid($uuid);
        if (!$user) {
            Log::warning("User not found for UUID: {$uuid}");
            return null;
        }

        // Check if user has active subscription
        // don't check for active subscription, because we want to show expired subscriptions too
        // if (!$user->hasActiveSubscription()) {
        //     Log::info("User has no active subscription: {$uuid}");
        //     return null;
        // }

        // Get server pool from current subscription or fallback to user's direct assignment
        $serverPool = $this->getUserServerPool($user);
        if (!$serverPool) {
            Log::warning("User has no assigned server pool: {$uuid}", [
                'user_id' => $user->id,
            ]);
            return null;
        }

        // Generate VLESS keys for all active servers in the pool
        $vlessKeys = $this->generateVlessKeysForUser($user, $serverPool);

        if (empty($vlessKeys)) {
            Log::warning("No VLESS keys generated for user: {$uuid}");
            return null;
        }

        $content = implode("\n", $vlessKeys);

        // Get traffic data from User model (centralized source)
        return new SubscriptionDto(
            content: $content,
            upload: $user->up_traffic,
            download: $user->down_traffic,
            total: $user->total_gb ?: 0 // 0 means unlimited
        );
    }

    /**
     * Generate VLESS keys for user from all active servers in pool.
     * Uses inbound data from database instead of API calls for better performance.
     */
    private function generateVlessKeysForUser(User $user, ServerPool $serverPool): array
    {
        $vlessKeys = [];

        // Get all active servers in the pool with their inbounds from database
        $servers = $serverPool->servers()
            ->where('is_active', true)
            ->with(['xuiInbounds' => function ($query) {
                $query->where('enable', true)
                      ->where('protocol', 'vless');
            }])
            ->get();

        foreach ($servers as $server) {
            try {
                // Use inbounds from database instead of API calls
                foreach ($server->xuiInbounds as $inbound) {
                    // Generate VLESS key for this inbound
                    $vlessKey = $this->generateVlessKeyFromInbound($user, $server, $inbound);
                    if ($vlessKey) {
                        $vlessKeys[] = $vlessKey;
                    }
                }
            } catch (\Exception $e) {
                Log::error("Failed to generate VLESS keys for server", [
                    'server_id' => $server->id,
                    'server_name' => $server->name,
                    'user_uuid' => $user->uuid,
                    'error' => $e->getMessage(),
                ]);
            }
        }

        return $vlessKeys;
    }



    /**
     * Generate VLESS key from inbound data.
     * Format: vless://uuid@host:port?params#remark
     */
    private function generateVlessKeyFromInbound(User $user, XuiServer $server, $inbound): ?string
    {
        try {
            // Use old_client_id if available, otherwise use uuid
            $clientId = $user->getClientId();
            $host = $server->host;

            // Handle both array (from API) and model (from DB) formats
            if (is_array($inbound)) {
                $port = $inbound['port'] ?? 443;
                $remark = $inbound['remark'] ?? $server->name;
                $streamSettings = json_decode($inbound['streamSettings'] ?? '{}', true);
            } else {
                // XuiInbound model
                $port = $inbound->port;
                $remark = $inbound->remark ?: $server->name;
                $streamSettings = $inbound->stream_settings ?: [];
            }

            $realitySettings = $streamSettings['realitySettings'] ?? [];

            // Build query parameters
            $params = [
                'type' => $streamSettings['network'] ?? 'tcp',
                'security' => $streamSettings['security'] ?? 'reality',
            ];

            // Add Reality-specific parameters
            if ($streamSettings['security'] === 'reality') {
                $params['pbk'] = $realitySettings['settings']['publicKey'] ?? '';
                $params['fp'] = $realitySettings['settings']['fingerprint'] ?? 'chrome';
                $params['sni'] = $realitySettings['serverNames'][0] ?? 'yahoo.com';
                $params['sid'] = $realitySettings['shortIds'][0] ?? '';
                $params['spx'] = $realitySettings['settings']['spiderX'] ?? '/';
                $params['flow'] = 'xtls-rprx-vision';
            }

            // Build query string
            $queryString = http_build_query($params);

            // Generate remark
            $remarkEncoded = urlencode($remark);

            // Add server load information if available
            if ($server->server_load !== null) {
                $urlRemark = $remark . ' ';
                // $loadPercentage = number_format($server->server_load, 2);
                // $urlRemark .= "({$loadPercentage}%)";
                $urlRemark .= " " . app(ServerStatusService::class)->toBrailleLoadChar($server->server_load);
                // $urlRemark .= " " . app(ServerStatusService::class)->toSuperscriptNumber($this->xuiServer->server_load);
                $urlRemark = str_replace(' ', ' ', $urlRemark);
                $remarkEncoded = urlencode($urlRemark);
            }



            // Build VLESS URL
            $vlessUrl = "vless://{$clientId}@{$host}:{$port}?{$queryString}#{$remarkEncoded}";

            return $vlessUrl;

        } catch (\Exception $e) {
            Log::error("Failed to generate VLESS key from inbound", [
                'server_id' => $server->id,
                'inbound_id' => is_array($inbound) ? ($inbound['id'] ?? 'unknown') : $inbound->inbound_id,
                'user_uuid' => $user->uuid,
                'error' => $e->getMessage(),
            ]);
            return null;
        }
    }

    /**
     * Create or update user clients on XUI servers.
     * This method ensures user has clients on all servers in their pool.
     */
    public function ensureUserClientsOnServers(User $user): bool
    {
        if (!$user->hasActiveSubscription()) {
            Log::info("User has no active subscription, skipping client creation", [
                'user_uuid' => $user->uuid,
            ]);
            return false;
        }

        // Get server pool from current subscription or fallback to user's direct assignment
        $serverPool = $this->getUserServerPool($user);
        if (!$serverPool) {
            Log::warning("User has no assigned server pool", [
                'user_uuid' => $user->uuid,
            ]);
            return false;
        }

        $success = true;
        $servers = $serverPool->servers()->where('is_active', true)->get();

        foreach ($servers as $server) {
            try {
                $this->ensureUserClientOnServer($user, $server);
            } catch (\Exception $e) {
                Log::error("Failed to ensure user client on server", [
                    'user_uuid' => $user->uuid,
                    'server_id' => $server->id,
                    'error' => $e->getMessage(),
                ]);
                $success = false;
            }
        }

        return $success;
    }

    /**
     * Ensure user has client on specific server.
     */
    private function ensureUserClientOnServer(User $user, XuiServer $server): void
    {
        // Get active VLESS inbounds from database
        $inbounds = $server->xuiInbounds()
            ->where('enable', true)
            ->where('protocol', 'vless')
            ->get();

        foreach ($inbounds as $inbound) {
            // Check if user already has client in this inbound
            if (!$this->userHasClientInInbound($user, $server, $inbound)) {
                // Create client in inbound
                $this->createUserClientInInbound($user, $server, $inbound);
            } else {
                // Update existing client if needed
                $this->updateUserClientInInbound($user, $server, $inbound);
            }
        }
    }

    /**
     * Check if user has client in specific inbound.
     */
    private function userHasClientInInbound(User $user, XuiServer $server, $inbound): bool
    {
        try {
            // Handle both array (from API) and model (from DB) formats
            if (is_array($inbound)) {
                $settings = json_decode($inbound['settings'] ?? '{}', true);
                $inboundId = $inbound['id'] ?? 'unknown';
            } else {
                // XuiInbound model
                $settings = $inbound->settings ?: [];
                $inboundId = $inbound->inbound_id;
            }

            $clients = $settings['clients'] ?? [];

            foreach ($clients as $client) {
                $clientEmail = User::extractUserEmailFromClientEmail($client['email'] ?? '');
                if ($clientEmail === $user->email) {
                    return true;
                }
            }

            return false;
        } catch (\Exception $e) {
            Log::error("Failed to check if user has client in inbound", [
                'user_uuid' => $user->uuid,
                'server_id' => $server->id,
                'inbound_id' => is_array($inbound) ? ($inbound['id'] ?? 'unknown') : $inbound->inbound_id,
                'error' => $e->getMessage(),
            ]);
            return false;
        }
    }

    /**
     * Create user client in specific inbound.
     */
    private function createUserClientInInbound(User $user, XuiServer $server, $inbound): void
    {
        try {
            $subscription = $user->currentSubscription;
            if (!$subscription) {
                throw new \Exception("User has no current subscription");
            }

            // Handle both array (from API) and model (from DB) formats
            $inboundId = is_array($inbound) ? $inbound['id'] : $inbound->inbound_id;

            $clientData = [
                'id' => $user->getClientId(), // Use old_client_id if available
                'flow' => 'xtls-rprx-vision',
                'email' => $user->generateClientEmail($inboundId),
                'limitIp' => 0,
                'totalGB' => $subscription->traffic_limit_gb * 1024 * 1024 * 1024, // Convert GB to bytes
                'expiryTime' => $subscription->expires_at->timestamp * 1000, // Convert to milliseconds
                'enable' => true,
                'tgId' => $user->tg_id ?? '',
                'subId' => '', // Can be generated if needed
                'comment' => "Auto-created for {$user->email}",
                'reset' => 0,
            ];

            $success = $this->xuiManagementService->createClient(
                $server,
                $inboundId,
                $clientData
            );

            if ($success) {
                Log::info("Created user client in inbound", [
                    'user_uuid' => $user->uuid,
                    'server_id' => $server->id,
                    'inbound_id' => $inboundId,
                ]);
            } else {
                throw new \Exception("Failed to add client to inbound via API");
            }
        } catch (\Exception $e) {
            Log::error("Failed to create user client in inbound", [
                'user_uuid' => $user->uuid,
                'server_id' => $server->id,
                'inbound_id' => is_array($inbound) ? ($inbound['id'] ?? 'unknown') : $inbound->inbound_id,
                'error' => $e->getMessage(),
            ]);
            throw $e;
        }
    }

    /**
     * Update user client in specific inbound.
     */
    private function updateUserClientInInbound(User $user, XuiServer $server, $inbound): void
    {
        try {
            $subscription = $user->currentSubscription;
            if (!$subscription) {
                return; // No active subscription, skip update
            }

            // Handle both array (from API) and model (from DB) formats
            $inboundId = is_array($inbound) ? $inbound['id'] : $inbound->inbound_id;

            $clientData = [
                'id' => $user->getClientId(), // Use old_client_id if available
                'totalGB' => $subscription->traffic_limit_gb * 1024 * 1024 * 1024,
                'expiryTime' => $subscription->expires_at->timestamp * 1000,
                'enable' => true,
            ];

            $success = $this->xuiManagementService->updateClient(
                $server,
                $inboundId,
                $user->getClientId(),
                $clientData
            );

            if ($success) {
                Log::debug("Updated user client in inbound", [
                    'user_uuid' => $user->uuid,
                    'server_id' => $server->id,
                    'inbound_id' => $inboundId,
                ]);
            }
        } catch (\Exception $e) {
            Log::error("Failed to update user client in inbound", [
                'user_uuid' => $user->uuid,
                'server_id' => $server->id,
                'inbound_id' => is_array($inbound) ? ($inbound['id'] ?? 'unknown') : $inbound->inbound_id,
                'error' => $e->getMessage(),
            ]);
        }
    }

    /**
     * Remove user clients from all servers.
     */
    public function removeUserClientsFromServers(User $user): bool
    {
        $serverPool = $this->getUserServerPool($user);
        if (!$serverPool) {
            return true; // No pool assigned, nothing to remove
        }

        $success = true;
        $servers = $serverPool->servers()->where('is_active', true)->get();

        foreach ($servers as $server) {
            try {
                $this->removeUserClientFromServer($user, $server);
            } catch (\Exception $e) {
                Log::error("Failed to remove user client from server", [
                    'user_uuid' => $user->uuid,
                    'server_id' => $server->id,
                    'error' => $e->getMessage(),
                ]);
                $success = false;
            }
        }

        return $success;
    }

    /**
     * Remove user client from specific server.
     */
    private function removeUserClientFromServer(User $user, XuiServer $server): void
    {
        $inbounds = $server->xuiInbounds()
            ->where('enable', true)
            ->where('protocol', 'vless')
            ->get();

        foreach ($inbounds as $inbound) {
            try {
                $success = $this->xuiManagementService->deleteClient(
                    $server,
                    $inbound->inbound_id,
                    $user->getClientId()
                );

                if ($success) {
                    Log::info("Removed user client from inbound", [
                        'user_uuid' => $user->uuid,
                        'server_id' => $server->id,
                        'inbound_id' => $inbound->inbound_id,
                    ]);
                }
            } catch (\Exception $e) {
                Log::error("Failed to remove user client from inbound", [
                    'user_uuid' => $user->uuid,
                    'server_id' => $server->id,
                    'inbound_id' => $inbound->inbound_id,
                    'error' => $e->getMessage(),
                ]);
            }
        }
    }

    /**
     * Update user traffic statistics from XUI servers.
     */
    public function updateUserTrafficFromServers(User $user): void
    {
        $serverPool = $this->getUserServerPool($user);
        if (!$serverPool) {
            return;
        }

        $totalUp = 0;
        $totalDown = 0;
        $servers = $serverPool->servers()->where('is_active', true)->get();

        foreach ($servers as $server) {
            try {
                $inbounds = $this->xuiManagementService->getInboundsList($server);

                foreach ($inbounds as $inbound) {
                    $clientStats = $inbound['clientStats'] ?? [];

                    foreach ($clientStats as $clientStat) {
                        // Find client by email (should match user email)
                        $clientEmail = User::extractUserEmailFromClientEmail($clientStat['email'] ?? '');
                        if (($clientEmail ?? '') === $user->email) {
                            $totalUp += $clientStat['up'] ?? 0;
                            $totalDown += $clientStat['down'] ?? 0;
                        }
                    }
                }
            } catch (\Exception $e) {
                Log::error("Failed to get traffic stats from server", [
                    'user_uuid' => $user->uuid,
                    'server_id' => $server->id,
                    'error' => $e->getMessage(),
                ]);
            }
        }

        // Update user traffic in database
        $user->update([
            'up_traffic' => $totalUp,
            'down_traffic' => $totalDown,
            'used_gb' => $totalUp + $totalDown,
        ]);

        Log::debug("Updated user traffic statistics", [
            'user_uuid' => $user->uuid,
            'up_traffic' => $totalUp,
            'down_traffic' => $totalDown,
        ]);
    }

    /**
     * Get server pool for user from current subscription or fallback to direct assignment.
     */
    private function getUserServerPool(User $user): ?ServerPool
    {
        // First try to get from current subscription
        $currentSubscription = $user->currentSubscription();
        if ($currentSubscription && $currentSubscription->server_pool_id) {
            return $currentSubscription->serverPool;
        }

        // Fallback to user's direct server pool assignment
        if ($user->server_pool_id) {
            return $user->serverPool;
        }

        // If still no pool, try to get from subscription plan
        if ($currentSubscription && $currentSubscription->subscriptionPlan) {
            // For now, we'll use the first available active pool
            // In the future, you might want to add server_pool_id to subscription_plans table
            return ServerPool::where('is_active', true)->first();
        }

        return null;
    }

    /**
     * Get mock inbound data for testing when servers are unavailable.
     */
    private function getMockInboundsForServer(XuiServer $server): array
    {
        return [
            [
                'id' => 1,
                'up' => 210567132380,
                'down' => 3771903651862,
                'total' => 0,
                'remark' => "🇷🇺 {$server->name}",
                'enable' => true,
                'expiryTime' => 0,
                'listen' => $server->host,
                'port' => 443,
                'protocol' => 'vless',
                'settings' => json_encode([
                    'clients' => [],
                    'decryption' => 'none',
                    'fallbacks' => []
                ]),
                'streamSettings' => json_encode([
                    'network' => 'tcp',
                    'security' => 'reality',
                    'externalProxy' => [],
                    'realitySettings' => [
                        'show' => false,
                        'xver' => 0,
                        'dest' => 'yahoo.com:443',
                        'serverNames' => [
                            'web.whatsapp.com',
                            'google.com',
                            'www.google.com',
                            'web.telegram.org',
                            'maps.google.com',
                            'whatsapp.com'
                        ],
                        'privateKey' => 'cAZB_OCTLq6ST4FSQRZBTx_gUMQQi3GcsEZV11cJ1S8',
                        'minClient' => '',
                        'maxClient' => '',
                        'maxTimediff' => 0,
                        'shortIds' => [
                            '',
                            '9cacd71f',
                            'b8027711e4',
                            '8a39',
                            '72a0',
                            'b3bd',
                            '5e87ad',
                            'fd',
                            'a45a5595',
                            '5dd0ad',
                            'd59a',
                            '99df',
                            '4815',
                            'f296ed',
                            '04e9dd9464',
                            'c655181bba65',
                            '331664',
                            'f618',
                            'da',
                            'f327',
                            '226294a2f88ab3',
                            'f5264a',
                            'da5508',
                            '66df0d8e76'
                        ],
                        'settings' => [
                            'publicKey' => 'qy9U8tY-9GWID2iqTihDTNhJDYLRQuNQPSN0rxyUW3w',
                            'fingerprint' => 'chrome',
                            'serverName' => '',
                            'spiderX' => '/'
                        ]
                    ],
                    'tcpSettings' => [
                        'acceptProxyProtocol' => false,
                        'header' => [
                            'type' => 'none'
                        ]
                    ]
                ]),
                'tag' => "inbound-{$server->host}:443",
                'sniffing' => json_encode([
                    'enabled' => true,
                    'destOverride' => [
                        'http',
                        'tls',
                        'quic',
                        'fakedns'
                    ],
                    'metadataOnly' => false,
                    'routeOnly' => false
                ]),
                'allocate' => ''
            ]
        ];
    }
}
