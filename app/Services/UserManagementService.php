<?php

namespace App\Services;

use App\Models\User;
use App\Models\SubscriptionPlan;
use App\Services\ReferralService;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

class UserManagementService
{
    public function __construct(
        private UserSubscriptionService $userSubscriptionService,
        private ReferralService $referralService
    ) {}

    /**
     * Create a new user with demo subscription.
     */
    public function createUserWithDemoSubscription(array $userData): ?User
    {
        try {
            DB::beginTransaction();

            // Get demo plan
            $demoplan = SubscriptionPlan::where('is_demo', true)->where('is_active', true)->first();
            if (!$demoplan) {
                Log::error("Demo plan not found");
                return null;
            }

            // Create user
            $user = User::create([
                'uuid' => $userData['uuid'] ?? Str::uuid()->toString(),
                'name' => $userData['name'] ?? '',
                'email' => $userData['email'],
                'tg_id' => $userData['tg_id'] ?? null,
                'comment' => $userData['comment'] ?? '',
                'source' => $userData['source'] ?? 'manual',
                'additional_data' => $userData['additional_data'] ?? null,
                'use_common_routing' => $userData['use_common_routing'] ?? false,
            ]);

            // Create demo subscription
            $subscription = $this->userSubscriptionService->createSubscription($user, $demoplan);

            if (!$subscription) {
                Log::error("Failed to create demo subscription for user", [
                    'user_id' => $user->id,
                ]);
                DB::rollBack();
                return null;
            }

            DB::commit();

            Log::info("User created with demo subscription", [
                'user_id' => $user->id,
                'user_email' => $user->email,
                'subscription_id' => $subscription->id,
                'source' => $user->source,
            ]);

            return $user;

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error("Failed to create user with demo subscription", [
                'email' => $userData['email'] ?? 'unknown',
                'error' => $e->getMessage(),
            ]);
            return null;
        }
    }

    /**
     * Create a user from LeadTeh.
     */
    public function createUserFromLeadteh(array $userData, string $contactId, ?string $referrerContactId = null): ?User
    {
        $userData['source'] = 'leadteh';
        $userData['additional_data'] = ['contact_id' => $contactId];

        if ($referrerContactId) {
            $userData['additional_data']['referrer_contact_id'] = $referrerContactId;
        }

        $user = $this->createUserWithDemoSubscription($userData);

        // Set referrer if provided
        if ($user && $referrerContactId) {
            $this->referralService->setUserReferrerByContactId($user, $referrerContactId);
        }

        return $user;
    }

    /**
     * Create a user from API.
     */
    public function createUserFromApi(array $userData, ?string $referralCode = null): ?User
    {
        $userData['source'] = 'api';

        $user = $this->createUserWithDemoSubscription($userData);

        // Set referrer if referral code provided
        if ($user && $referralCode) {
            $this->referralService->processReferralFromLink($referralCode, $user);
        }

        return $user;
    }

    /**
     * Create a user manually (admin).
     */
    public function createUserManually(array $userData): ?User
    {
        $userData['source'] = 'manual';

        return $this->createUserWithDemoSubscription($userData);
    }

    /**
     * Delete user and cleanup.
     */
    public function deleteUser(User $user): bool
    {
        try {
            DB::beginTransaction();

            // Expire subscription first (this will trigger XUI client deletion)
            $this->userSubscriptionService->expireSubscription($user);

            // Soft delete user
            $user->delete();

            DB::commit();

            Log::info("User deleted successfully", [
                'user_id' => $user->id,
                'user_email' => $user->email,
            ]);

            return true;

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error("Failed to delete user", [
                'user_id' => $user->id,
                'error' => $e->getMessage(),
            ]);
            return false;
        }
    }

    /**
     * Disable user (expire subscription but keep user).
     */
    public function disableUser(User $user): bool
    {
        try {
            // Expire subscription (this will trigger XUI client deletion)
            $success = $this->userSubscriptionService->expireSubscription($user);

            if ($success) {
                $user->update(['disabled_at' => now()]);

                Log::info("User disabled successfully", [
                    'user_id' => $user->id,
                    'user_email' => $user->email,
                ]);
            }

            return $success;

        } catch (\Exception $e) {
            Log::error("Failed to disable user", [
                'user_id' => $user->id,
                'error' => $e->getMessage(),
            ]);
            return false;
        }
    }

    /**
     * Enable user (restore from disabled state).
     */
    public function enableUser(User $user, ?SubscriptionPlan $plan = null): bool
    {
        try {
            // Use demo plan if no plan specified
            if (!$plan) {
                $plan = SubscriptionPlan::where('is_demo', true)->where('is_active', true)->first();
                if (!$plan) {
                    Log::error("Demo plan not found for user enable");
                    return false;
                }
            }

            // Create new subscription
            $subscription = $this->userSubscriptionService->createSubscription($user, $plan);

            if ($subscription) {
                $user->update(['disabled_at' => null]);

                Log::info("User enabled successfully", [
                    'user_id' => $user->id,
                    'user_email' => $user->email,
                    'subscription_id' => $subscription->id,
                ]);

                return true;
            }

            return false;

        } catch (\Exception $e) {
            Log::error("Failed to enable user", [
                'user_id' => $user->id,
                'error' => $e->getMessage(),
            ]);
            return false;
        }
    }

    /**
     * Get user statistics.
     */
    public function getUserStats(User $user): array
    {
        $subscriptionStats = $this->userSubscriptionService->getUserSubscriptionStats($user);

        return array_merge($subscriptionStats, [
            'user_id' => $user->id,
            'user_email' => $user->email,
            'user_name' => $user->name,
            'source' => $user->source,
            'created_at' => $user->created_at,
            'disabled_at' => $user->disabled_at,
            'is_disabled' => (bool) $user->disabled_at,
        ]);
    }
}
