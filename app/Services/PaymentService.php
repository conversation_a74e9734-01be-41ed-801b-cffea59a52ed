<?php

namespace App\Services;

use App\Models\User;
use App\Models\Order;
use App\Models\Payment;
use App\Models\SubscriptionPlan;
use App\Events\PaymentReceived;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class PaymentService
{
    public function __construct(
        private TbankAcquiringService $tbankService
    ) {}

    /**
     * Create order for user subscription.
     */
    public function createOrder(User $user, SubscriptionPlan $plan): ?Order
    {
        try {
            DB::beginTransaction();

            $finalPrice = $plan->getFinalPriceInCents();
            $expiresAt = now()->addHours(24); // Order expires in 24 hours

            $order = Order::create([
                'user_id' => $user->id,
                'subscription_plan_id' => $plan->id,
                'amount_in_cents' => $finalPrice,
                'currency' => 'RUB',
                'status' => 'pending',
                'description' => "Подписка: {$plan->name} для {$user->email}",
                'expires_at' => $expiresAt,
            ]);

            DB::commit();

            Log::info("Order created", [
                'order_id' => $order->id,
                'user_id' => $user->id,
                'plan_id' => $plan->id,
                'amount_in_cents' => $finalPrice,
                'expires_at' => $expiresAt->toDateTimeString(),
            ]);

            return $order;

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error("Failed to create order", [
                'user_id' => $user->id,
                'plan_id' => $plan->id,
                'error' => $e->getMessage(),
            ]);
            return null;
        }
    }

    /**
     * Process payment with specified method.
     */
    public function processPayment(Order $order, string $method, array $data = []): ?Payment
    {
        try {
            DB::beginTransaction();

            // Create payment record
            $payment = Payment::create([
                'order_id' => $order->id,
                'method' => $method,
                'status' => 'pending',
                'amount_in_cents' => $order->amount_in_cents,
                'external_details' => $data,
            ]);

            // Process based on method
            $success = match($method) {
                'tbank' => $this->processTbankPayment($payment, $data),
                'manual' => $this->processManualPayment($payment, $data),
                'balance' => $this->processBalancePayment($payment, $data),
                default => false,
            };

            if ($success) {
                DB::commit();
                Log::info("Payment processed successfully", [
                    'payment_id' => $payment->id,
                    'order_id' => $order->id,
                    'method' => $method,
                ]);
                return $payment;
            } else {
                DB::rollBack();
                Log::error("Payment processing failed", [
                    'payment_id' => $payment->id,
                    'order_id' => $order->id,
                    'method' => $method,
                ]);
                return null;
            }

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error("Exception processing payment", [
                'order_id' => $order->id,
                'method' => $method,
                'error' => $e->getMessage(),
            ]);
            return null;
        }
    }

    /**
     * Process T-Bank payment.
     */
    private function processTbankPayment(Payment $payment, array $data): bool
    {
        try {
            $order = $payment->order;

            $notificationURL = route('payment.webhook.tbank');

            $successUrl = str_replace(
                        ['{APP_URL}', '{uuid}'],
                        [config('app.url'), $data['uuid']],
                        config('services.tbank.success_url')
            );

            $failedUrl = str_replace(
                ['{APP_URL}', '{uuid}'],
                [config('app.url'), $data['uuid']],
                config('services.tbank.failed_url')
            );

            // Create payment in T-Bank
            $tbankResponse = $this->tbankService->createPayment([
                'Amount' => $payment->amount_in_cents,
                'OrderId' => $payment->reference_id ?? $payment->generatePaymentReference(),
                'Description' => $order->description,
                'CustomerKey' => $order->user->email,
                'NotificationURL' => $notificationURL,
                'SuccessURL' => $successUrl,
                'FailURL' => $failedUrl,
            ]);

            if ($tbankResponse && isset($tbankResponse['PaymentURL'])) {
                $payment->update([
                    'external_id' => $tbankResponse['PaymentId'],
                    'payment_url' => $tbankResponse['PaymentURL'],
                    'status' => 'processing',
                    'external_details' => array_merge(
                        $payment->external_details ?? [],
                        ['tbank_response' => $tbankResponse]
                    ),
                ]);

                Log::info("T-Bank payment created", [
                    'payment_id' => $payment->id,
                    'tbank_payment_id' => $tbankResponse['PaymentId'],
                    'payment_url' => $tbankResponse['PaymentURL'],
                ]);

                return true;
            }

            Log::error("T-Bank payment creation failed", [
                'payment_id' => $payment->id,
                'response' => $tbankResponse,
            ]);

            return false;

        } catch (\Exception $e) {
            Log::error("Exception creating T-Bank payment", [
                'payment_id' => $payment->id,
                'error' => $e->getMessage(),
            ]);
            return false;
        }
    }

    /**
     * Process manual payment (admin).
     */
    private function processManualPayment(Payment $payment, array $data): bool
    {
        try {
            $payment->update([
                'status' => 'completed',
                'processed_at' => now(),
                'external_details' => array_merge(
                    $payment->external_details ?? [],
                    [
                        'manual_note' => $data['note'] ?? 'Manual payment by admin',
                        'processed_by' => $data['admin_id'] ?? 'system',
                    ]
                ),
            ]);

            // Dispatch payment received event
            PaymentReceived::dispatch($payment, $payment->order);

            Log::info("Manual payment completed", [
                'payment_id' => $payment->id,
                'processed_by' => $data['admin_id'] ?? 'system',
            ]);

            return true;

        } catch (\Exception $e) {
            Log::error("Exception processing manual payment", [
                'payment_id' => $payment->id,
                'error' => $e->getMessage(),
            ]);
            return false;
        }
    }

    /**
     * Process balance payment (API).
     */
    private function processBalancePayment(Payment $payment, array $data): bool
    {
        try {
            // For balance payments, we assume external system has already verified funds
            $payment->update([
                'status' => 'completed',
                'processed_at' => now(),
                'external_details' => array_merge(
                    $payment->external_details ?? [],
                    [
                        'balance_transaction_id' => $data['transaction_id'] ?? null,
                        'external_system' => $data['system'] ?? 'unknown',
                    ]
                ),
            ]);

            // Dispatch payment received event
            PaymentReceived::dispatch($payment, $payment->order);

            Log::info("Balance payment completed", [
                'payment_id' => $payment->id,
                'transaction_id' => $data['transaction_id'] ?? null,
            ]);

            return true;

        } catch (\Exception $e) {
            Log::error("Exception processing balance payment", [
                'payment_id' => $payment->id,
                'error' => $e->getMessage(),
            ]);
            return false;
        }
    }

    /**
     * Handle T-Bank webhook.
     */
    public function handleTbankWebhook(array $data): bool
    {
        try {
            // Verify webhook signature
            if (!$this->tbankService->verifyWebhookSignature($data)) {
                Log::warning("Invalid T-Bank webhook signature", ['data' => $data]);
                return false;
            }

            $paymentId = $data['PaymentId'] ?? null;
            $status = $data['Status'] ?? null;

            if (!$paymentId || !$status) {
                Log::warning("Invalid T-Bank webhook data", ['data' => $data]);
                return false;
            }

            // Find payment by external ID
            $payment = Payment::where('external_id', $paymentId)
                ->where('method', 'tbank')
                ->first();

            if (!$payment) {
                Log::warning("Payment not found for T-Bank webhook", [
                    'payment_id' => $paymentId,
                    'status' => $status,
                ]);
                return false;
            }

            return $this->updatePaymentFromWebhook($payment, $data);

        } catch (\Exception $e) {
            Log::error("Exception handling T-Bank webhook", [
                'data' => $data,
                'error' => $e->getMessage(),
            ]);
            return false;
        }
    }

    /**
     * Update payment status from webhook.
     */
    private function updatePaymentFromWebhook(Payment $payment, array $data): bool
    {
        try {
            DB::beginTransaction();

            $status = $data['Status'];
            $oldStatus = $payment->status;

            // Update payment details
            $payment->updateExternalDetails(['webhook_data' => $data]);

            // Handle status changes
            switch ($status) {
                case 'CONFIRMED':
                    // CONFIRMED means payment is fully confirmed
                    if ($payment->status !== 'completed') {
                        $payment->markAsCompleted();
                        PaymentReceived::dispatch($payment, $payment->order);

                        Log::info("T-Bank payment successful", [
                            'payment_id' => $payment->id,
                            'order_id' => $payment->order_id,
                            'amount' => $payment->getAmount(),
                            'status' => $status,
                        ]);
                    }
                    break;

                case 'REJECTED':
                case 'CANCELLED':
                case 'DEADLINE_EXPIRED':
                    if ($payment->status !== 'failed' && $payment->status !== 'cancelled') {
                        $payment->markAsFailed();

                        Log::info("T-Bank payment failed/cancelled", [
                            'payment_id' => $payment->id,
                            'order_id' => $payment->order_id,
                            'status' => $status,
                        ]);
                    }
                    break;

                case 'NEW':
                case 'FORM_SHOWED':
                case '3DS_CHECKING':
                case '3DS_CHECKED':
                case 'AUTHORIZED':
                    // AUTHORIZED means payment is authorized and funds are blocked
                    // NEW means payment is created but not yet confirmed
                    // These are intermediate statuses, just update the payment
                    Log::debug("T-Bank payment status update (intermediate)", [
                        'payment_id' => $payment->id,
                        'old_status' => $oldStatus,
                        'new_status' => $status,
                    ]);
                    break;

                default:
                    Log::warning("Unknown T-Bank payment status", [
                        'payment_id' => $payment->id,
                        'old_status' => $oldStatus,
                        'new_status' => $status,
                    ]);
            }

            DB::commit();
            return true;

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error("Exception updating payment from webhook", [
                'payment_id' => $payment->id,
                'webhook_data' => $data,
                'error' => $e->getMessage(),
            ]);
            return false;
        }
    }

    /**
     * Get payment URL for order.
     */
    public function getPaymentUrl(Order $order): ?string
    {
        $payment = $order->payments()
            ->where('status', 'processing')
            ->whereNotNull('payment_url')
            ->first();

        return $payment?->payment_url;
    }

    /**
     * Cancel expired orders.
     */
    public function cancelExpiredOrders(): int
    {
        $expiredOrders = Order::where('status', 'pending')
            ->where('expires_at', '<', now())
            ->get();

        $cancelledCount = 0;

        foreach ($expiredOrders as $order) {
            try {
                $order->markAsExpired();
                $cancelledCount++;

                Log::info("Order expired and cancelled", [
                    'order_id' => $order->id,
                    'user_id' => $order->user_id,
                ]);

            } catch (\Exception $e) {
                Log::error("Failed to cancel expired order", [
                    'order_id' => $order->id,
                    'error' => $e->getMessage(),
                ]);
            }
        }

        if ($cancelledCount > 0) {
            Log::info("Cancelled expired orders", [
                'count' => $cancelledCount,
            ]);
        }

        return $cancelledCount;
    }
}
