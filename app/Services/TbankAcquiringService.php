<?php

namespace App\Services;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class TbankAcquiringService
{
    private ?string $terminalKey;
    private ?string $secretKey;
    private string $apiUrl;

    public function __construct()
    {
        $this->terminalKey = config('services.tbank.terminal_key');
        $this->secretKey = config('services.tbank.secret_key');
        $this->apiUrl = config('services.tbank.api_url', 'https://securepay.tinkoff.ru/v2/');
    }

    /**
     * Create payment in T-Bank.
     */
    public function createPayment(array $data): ?array
    {
        try {
            $requestData = array_merge($data, [
                'TerminalKey' => $this->terminalKey,
            ]);

            // Generate token
            $requestData['Token'] = $this->generateToken($requestData);

            $response = Http::timeout(30)
                ->post($this->apiUrl . 'Init', $requestData);

            if ($response->successful()) {
                $responseData = $response->json();

                if ($responseData['Success'] ?? false) {
                    Log::info("T-Bank payment created successfully", [
                        'payment_id' => $responseData['PaymentId'] ?? null,
                        'order_id' => $data['OrderId'] ?? null,
                    ]);

                    return $responseData;
                }
            }

            Log::error("T-Bank payment creation failed", [
                'status' => $response->status(),
                'response' => $response->json(),
                'request_data' => $this->sanitizeLogData($requestData),
            ]);

            return null;

        } catch (\Exception $e) {
            Log::error("Exception creating T-Bank payment", [
                'error' => $e->getMessage(),
                'request_data' => $this->sanitizeLogData($data),
            ]);
            return null;
        }
    }

    /**
     * Get payment status from T-Bank.
     */
    public function getPaymentStatus(string $paymentId): ?array
    {
        try {
            $requestData = [
                'TerminalKey' => $this->terminalKey,
                'PaymentId' => $paymentId,
            ];

            $requestData['Token'] = $this->generateToken($requestData);

            $response = Http::timeout(30)
                ->post($this->apiUrl . 'GetState', $requestData);

            if ($response->successful()) {
                $responseData = $response->json();

                if ($responseData['Success'] ?? false) {
                    Log::debug("T-Bank payment status retrieved", [
                        'payment_id' => $paymentId,
                        'status' => $responseData['Status'] ?? 'unknown',
                    ]);

                    return $responseData;
                }
            }

            Log::error("T-Bank payment status request failed", [
                'payment_id' => $paymentId,
                'status' => $response->status(),
                'response' => $response->json(),
            ]);

            return null;

        } catch (\Exception $e) {
            Log::error("Exception getting T-Bank payment status", [
                'payment_id' => $paymentId,
                'error' => $e->getMessage(),
            ]);
            return null;
        }
    }

    /**
     * Cancel payment in T-Bank.
     */
    public function cancelPayment(string $paymentId): bool
    {
        try {
            $requestData = [
                'TerminalKey' => $this->terminalKey,
                'PaymentId' => $paymentId,
            ];

            $requestData['Token'] = $this->generateToken($requestData);

            $response = Http::timeout(30)
                ->post($this->apiUrl . 'Cancel', $requestData);

            if ($response->successful()) {
                $responseData = $response->json();

                if ($responseData['Success'] ?? false) {
                    Log::info("T-Bank payment cancelled", [
                        'payment_id' => $paymentId,
                    ]);

                    return true;
                }
            }

            Log::error("T-Bank payment cancellation failed", [
                'payment_id' => $paymentId,
                'status' => $response->status(),
                'response' => $response->json(),
            ]);

            return false;

        } catch (\Exception $e) {
            Log::error("Exception cancelling T-Bank payment", [
                'payment_id' => $paymentId,
                'error' => $e->getMessage(),
            ]);
            return false;
        }
    }

    /**
     * Verify webhook signature.
     */
    public function verifyWebhookSignature(array $data): bool
    {
        // Temporary: Skip signature verification for testing
        // Remove this when signature verification is fixed
        if (config('app.debug') && config('services.tbank.skip_signature_verification', false)) {
            Log::warning("T-Bank webhook signature verification SKIPPED (debug mode)");
            return true;
        }

        try {
            $receivedToken = $data['Token'] ?? '';

            // Create a copy for token generation
            $dataForToken = $data;

            // Remove Token field
            unset($dataForToken['Token']);

            // Remove fields that should not be included in webhook signature according to T-Bank docs
            // These fields are not included in webhook signature calculation
            $excludeFields = [
                'Success',
                'ErrorCode',
                'Message',
                'Details',
                'CardId',
                'Pan',
                'ExpDate',
                'RebillId',
                'Data'
            ];

            foreach ($excludeFields as $field) {
                unset($dataForToken[$field]);
            }

            $expectedToken = $this->generateToken($dataForToken);

            $isValid = hash_equals($expectedToken, $receivedToken);

            if (!$isValid) {
                // Try different approaches for debugging
                $this->debugTokenGeneration($data, $receivedToken);

                // Try alternative webhook verification method
                $alternativeValid = $this->verifyWebhookSignatureAlternative($data);

                Log::warning("T-Bank webhook signature verification failed", [
                    'received_token' => $receivedToken,
                    'expected_token' => $expectedToken,
                    'alternative_verification' => $alternativeValid,
                    'data_for_token' => $this->sanitizeLogData($dataForToken),
                    'original_data' => $this->sanitizeLogData($data),
                ]);

                // If alternative method works, use it
                if ($alternativeValid) {
                    Log::info("T-Bank webhook verified using alternative method");
                    return true;
                }
            } else {
                Log::debug("T-Bank webhook signature verified successfully", [
                    'payment_id' => $data['PaymentId'] ?? null,
                    'status' => $data['Status'] ?? null,
                ]);
            }

            return $isValid;

        } catch (\Exception $e) {
            Log::error("Exception verifying T-Bank webhook signature", [
                'error' => $e->getMessage(),
                'data' => $this->sanitizeLogData($data),
            ]);
            return false;
        }
    }

    /**
     * Generate token for T-Bank API.
     */
    private function generateToken(array $data): string
    {
        // Remove Token if present
        unset($data['Token']);

        // Add secret key
        $data['Password'] = $this->secretKey;

        // Convert all values to strings and ensure proper types
        foreach ($data as $key => $value) {
            if (is_bool($value)) {
                $data[$key] = $value ? 'true' : 'false';
            } elseif (is_numeric($value)) {
                $data[$key] = (string) $value;
            } elseif (is_array($value)) {
                $data[$key] = json_encode($value, JSON_UNESCAPED_UNICODE);
            } else {
                $data[$key] = (string) $value;
            }
        }

        // Sort by key
        ksort($data);

        // Create string for hashing
        $tokenString = '';
        foreach ($data as $value) {
            $tokenString .= $value;
        }

        // Log token generation for debugging (only in debug mode)
        if (config('app.debug')) {
            Log::debug("T-Bank token generation", [
                'data_keys' => array_keys($data),
                'data_values' => array_values($data),
                'token_string_length' => strlen($tokenString),
                'token_string_preview' => substr($tokenString, 0, 100) . '...',
                'token_string_full' => $tokenString, // Full string for debugging
            ]);
        }

        return hash('sha256', $tokenString);
    }

    /**
     * Debug token generation with different approaches.
     */
    private function debugTokenGeneration(array $originalData, string $receivedToken): void
    {
        if (!config('app.debug')) {
            return;
        }

        Log::debug("=== T-Bank Token Debug Session ===");

        // Test 1: Original approach (all fields except excluded)
        $test1Data = $originalData;
        unset($test1Data['Token']);
        $excludeFields = ['Success', 'ErrorCode', 'Message', 'Details', 'CardId', 'Pan', 'ExpDate', 'RebillId', 'Data'];
        foreach ($excludeFields as $field) {
            unset($test1Data[$field]);
        }
        $test1Token = $this->generateTokenDebug($test1Data, "Test 1: Exclude standard fields");

        // Test 2: Only core fields
        $test2Data = [
            'TerminalKey' => $originalData['TerminalKey'],
            'OrderId' => $originalData['OrderId'],
            'Status' => $originalData['Status'],
            'PaymentId' => $originalData['PaymentId'],
            'Amount' => $originalData['Amount'],
        ];
        $test2Token = $this->generateTokenDebug($test2Data, "Test 2: Only core fields");

        // Test 3: All fields except Token, Success, ErrorCode
        $test3Data = $originalData;
        unset($test3Data['Token'], $test3Data['Success'], $test3Data['ErrorCode']);
        $test3Token = $this->generateTokenDebug($test3Data, "Test 3: All except Token, Success, ErrorCode");

        // Test 4: PaymentId as string
        $test4Data = $test1Data;
        $test4Data['PaymentId'] = (string) $test4Data['PaymentId'];
        $test4Token = $this->generateTokenDebug($test4Data, "Test 4: PaymentId as string");

        Log::debug("T-Bank Token Debug Results", [
            'received_token' => $receivedToken,
            'test1_match' => $test1Token === $receivedToken,
            'test2_match' => $test2Token === $receivedToken,
            'test3_match' => $test3Token === $receivedToken,
            'test4_match' => $test4Token === $receivedToken,
        ]);
    }

    /**
     * Generate token for debugging purposes.
     */
    private function generateTokenDebug(array $data, string $testName): string
    {
        $data['Password'] = $this->secretKey;
        ksort($data);

        $tokenString = '';
        foreach ($data as $value) {
            if (is_bool($value)) {
                $value = $value ? 'true' : 'false';
            } elseif (is_numeric($value)) {
                $value = (string) $value;
            } elseif (is_array($value)) {
                $value = json_encode($value, JSON_UNESCAPED_UNICODE);
            } else {
                $value = (string) $value;
            }
            $tokenString .= $value;
        }

        $token = hash('sha256', $tokenString);

        Log::debug($testName, [
            'fields' => array_keys($data),
            'token_string' => $tokenString,
            'token' => $token,
        ]);

        return $token;
    }

    /**
     * Alternative webhook signature verification method.
     * Based on T-Bank documentation examples.
     */
    private function verifyWebhookSignatureAlternative(array $data): bool
    {
        try {
            $receivedToken = $data['Token'] ?? '';

            // Method 1: Only specific fields in specific order
            $method1Data = [
                'TerminalKey' => $data['TerminalKey'],
                'OrderId' => $data['OrderId'],
                'Success' => $data['Success'] ? 'true' : 'false',
                'Status' => $data['Status'],
                'PaymentId' => (string) $data['PaymentId'],
                'ErrorCode' => $data['ErrorCode'],
                'Amount' => (string) $data['Amount'],
                'Password' => $this->secretKey,
            ];

            ksort($method1Data);
            $tokenString1 = implode('', array_values($method1Data));
            $token1 = hash('sha256', $tokenString1);

            if (config('app.debug')) {
                Log::debug("Alternative method 1", [
                    'token_string' => $tokenString1,
                    'token' => $token1,
                    'match' => $token1 === $receivedToken,
                ]);
            }

            if ($token1 === $receivedToken) {
                return true;
            }

            // Method 2: Without Success and ErrorCode
            $method2Data = [
                'TerminalKey' => $data['TerminalKey'],
                'OrderId' => $data['OrderId'],
                'Status' => $data['Status'],
                'PaymentId' => (string) $data['PaymentId'],
                'Amount' => (string) $data['Amount'],
                'Password' => $this->secretKey,
            ];

            ksort($method2Data);
            $tokenString2 = implode('', array_values($method2Data));
            $token2 = hash('sha256', $tokenString2);

            if (config('app.debug')) {
                Log::debug("Alternative method 2", [
                    'token_string' => $tokenString2,
                    'token' => $token2,
                    'match' => $token2 === $receivedToken,
                ]);
            }

            return $token2 === $receivedToken;

        } catch (\Exception $e) {
            Log::error("Exception in alternative webhook verification", [
                'error' => $e->getMessage(),
            ]);
            return false;
        }
    }

    /**
     * Sanitize data for logging (remove sensitive information).
     */
    private function sanitizeLogData(array $data): array
    {
        $sanitized = $data;

        // Remove sensitive fields
        unset($sanitized['Token']);
        unset($sanitized['Password']);

        return $sanitized;
    }

    /**
     * Format amount for T-Bank (cents).
     */
    public function formatAmount(float $amount): int
    {
        return (int) round($amount * 100);
    }

    /**
     * Parse amount from T-Bank (cents to major units).
     */
    public function parseAmount(int $cents): float
    {
        return $cents / 100;
    }

    /**
     * Get supported payment methods.
     */
    public function getSupportedMethods(): array
    {
        return [
            'card' => 'Банковская карта',
            'sbp' => 'Система быстрых платежей',
            'mobile' => 'Мобильные платежи',
        ];
    }

    /**
     * Check if T-Bank service is configured.
     */
    public function isConfigured(): bool
    {
        return !empty($this->terminalKey) && !empty($this->secretKey);
    }

    /**
     * Test T-Bank connection.
     */
    public function testConnection(): bool
    {
        try {
            if (!$this->isConfigured()) {
                Log::error("T-Bank service not configured");
                return false;
            }

            // Try to get terminal info
            $requestData = [
                'TerminalKey' => $this->terminalKey,
            ];

            $requestData['Token'] = $this->generateToken($requestData);

            $response = Http::timeout(10)
                ->post($this->apiUrl . 'GetTerminals', $requestData);

            if ($response->successful()) {
                $responseData = $response->json();

                if ($responseData['Success'] ?? false) {
                    Log::info("T-Bank connection test successful");
                    return true;
                }
            }

            Log::error("T-Bank connection test failed", [
                'status' => $response->status(),
                'response' => $response->json(),
            ]);

            return false;

        } catch (\Exception $e) {
            Log::error("Exception testing T-Bank connection", [
                'error' => $e->getMessage(),
            ]);
            return false;
        }
    }
}
