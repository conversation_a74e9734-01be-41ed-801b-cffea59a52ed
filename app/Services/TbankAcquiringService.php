<?php

namespace App\Services;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class TbankAcquiringService
{
    private ?string $terminalKey;
    private ?string $secretKey;
    private string $apiUrl;

    public function __construct()
    {
        $this->terminalKey = config('services.tbank.terminal_key');
        $this->secretKey = config('services.tbank.secret_key');
        $this->apiUrl = config('services.tbank.api_url', 'https://securepay.tinkoff.ru/v2/');
    }

    /**
     * Create payment in T-Bank.
     */
    public function createPayment(array $data): ?array
    {
        try {
            $requestData = array_merge($data, [
                'TerminalKey' => $this->terminalKey,
            ]);

            // Generate token
            $requestData['Token'] = $this->generateToken($requestData);

            $response = Http::timeout(30)
                ->post($this->apiUrl . 'Init', $requestData);

            if ($response->successful()) {
                $responseData = $response->json();

                if ($responseData['Success'] ?? false) {
                    Log::info("T-Bank payment created successfully", [
                        'payment_id' => $responseData['PaymentId'] ?? null,
                        'order_id' => $data['OrderId'] ?? null,
                    ]);

                    return $responseData;
                }
            }

            Log::error("T-Bank payment creation failed", [
                'status' => $response->status(),
                'response' => $response->json(),
                'request_data' => $this->sanitizeLogData($requestData),
            ]);

            return null;

        } catch (\Exception $e) {
            Log::error("Exception creating T-Bank payment", [
                'error' => $e->getMessage(),
                'request_data' => $this->sanitizeLogData($data),
            ]);
            return null;
        }
    }

    /**
     * Get payment status from T-Bank.
     */
    public function getPaymentStatus(string $paymentId): ?array
    {
        try {
            $requestData = [
                'TerminalKey' => $this->terminalKey,
                'PaymentId' => $paymentId,
            ];

            $requestData['Token'] = $this->generateToken($requestData);

            $response = Http::timeout(30)
                ->post($this->apiUrl . 'GetState', $requestData);

            if ($response->successful()) {
                $responseData = $response->json();

                if ($responseData['Success'] ?? false) {
                    Log::debug("T-Bank payment status retrieved", [
                        'payment_id' => $paymentId,
                        'status' => $responseData['Status'] ?? 'unknown',
                    ]);

                    return $responseData;
                }
            }

            Log::error("T-Bank payment status request failed", [
                'payment_id' => $paymentId,
                'status' => $response->status(),
                'response' => $response->json(),
            ]);

            return null;

        } catch (\Exception $e) {
            Log::error("Exception getting T-Bank payment status", [
                'payment_id' => $paymentId,
                'error' => $e->getMessage(),
            ]);
            return null;
        }
    }

    /**
     * Cancel payment in T-Bank.
     */
    public function cancelPayment(string $paymentId): bool
    {
        try {
            $requestData = [
                'TerminalKey' => $this->terminalKey,
                'PaymentId' => $paymentId,
            ];

            $requestData['Token'] = $this->generateToken($requestData);

            $response = Http::timeout(30)
                ->post($this->apiUrl . 'Cancel', $requestData);

            if ($response->successful()) {
                $responseData = $response->json();

                if ($responseData['Success'] ?? false) {
                    Log::info("T-Bank payment cancelled", [
                        'payment_id' => $paymentId,
                    ]);

                    return true;
                }
            }

            Log::error("T-Bank payment cancellation failed", [
                'payment_id' => $paymentId,
                'status' => $response->status(),
                'response' => $response->json(),
            ]);

            return false;

        } catch (\Exception $e) {
            Log::error("Exception cancelling T-Bank payment", [
                'payment_id' => $paymentId,
                'error' => $e->getMessage(),
            ]);
            return false;
        }
    }

    /**
     * Verify webhook signature.
     */
    public function verifyWebhookSignature(array $data): bool
    {
        try {
            $receivedToken = $data['Token'] ?? '';

            // Create a copy for token generation
            $dataForToken = $data;

            // Remove Token field
            unset($dataForToken['Token']);

            // Remove fields that should not be included in webhook signature according to T-Bank docs
            // These fields are not included in webhook signature calculation
            $excludeFields = [
                'Success',
                'ErrorCode',
                'Message',
                'Details',
                'CardId',
                'Pan',
                'ExpDate',
                'RebillId',
                'Data'
            ];

            foreach ($excludeFields as $field) {
                unset($dataForToken[$field]);
            }

            $expectedToken = $this->generateToken($dataForToken);

            $isValid = hash_equals($expectedToken, $receivedToken);

            if (!$isValid) {
                Log::warning("T-Bank webhook signature verification failed", [
                    'received_token' => $receivedToken,
                    'expected_token' => $expectedToken,
                    'data_for_token' => $this->sanitizeLogData($dataForToken),
                    'original_data' => $this->sanitizeLogData($data),
                ]);
            } else {
                Log::debug("T-Bank webhook signature verified successfully", [
                    'payment_id' => $data['PaymentId'] ?? null,
                    'status' => $data['Status'] ?? null,
                ]);
            }

            return $isValid;

        } catch (\Exception $e) {
            Log::error("Exception verifying T-Bank webhook signature", [
                'error' => $e->getMessage(),
                'data' => $this->sanitizeLogData($data),
            ]);
            return false;
        }
    }

    /**
     * Generate token for T-Bank API.
     */
    private function generateToken(array $data): string
    {
        // Remove Token if present
        unset($data['Token']);

        // Add secret key
        $data['Password'] = $this->secretKey;

        // Sort by key
        ksort($data);

        // Create string for hashing
        $tokenString = '';
        foreach ($data as $value) {
            if (is_array($value)) {
                $value = json_encode($value);
            }
            $tokenString .= $value;
        }

        // Log token generation for debugging (only in debug mode)
        if (config('app.debug')) {
            Log::debug("T-Bank token generation", [
                'data_keys' => array_keys($data),
                'token_string_length' => strlen($tokenString),
                'token_string_preview' => substr($tokenString, 0, 100) . '...',
            ]);
        }

        return hash('sha256', $tokenString);
    }

    /**
     * Sanitize data for logging (remove sensitive information).
     */
    private function sanitizeLogData(array $data): array
    {
        $sanitized = $data;

        // Remove sensitive fields
        unset($sanitized['Token']);
        unset($sanitized['Password']);

        return $sanitized;
    }

    /**
     * Format amount for T-Bank (cents).
     */
    public function formatAmount(float $amount): int
    {
        return (int) round($amount * 100);
    }

    /**
     * Parse amount from T-Bank (cents to major units).
     */
    public function parseAmount(int $cents): float
    {
        return $cents / 100;
    }

    /**
     * Get supported payment methods.
     */
    public function getSupportedMethods(): array
    {
        return [
            'card' => 'Банковская карта',
            'sbp' => 'Система быстрых платежей',
            'mobile' => 'Мобильные платежи',
        ];
    }

    /**
     * Check if T-Bank service is configured.
     */
    public function isConfigured(): bool
    {
        return !empty($this->terminalKey) && !empty($this->secretKey);
    }

    /**
     * Test T-Bank connection.
     */
    public function testConnection(): bool
    {
        try {
            if (!$this->isConfigured()) {
                Log::error("T-Bank service not configured");
                return false;
            }

            // Try to get terminal info
            $requestData = [
                'TerminalKey' => $this->terminalKey,
            ];

            $requestData['Token'] = $this->generateToken($requestData);

            $response = Http::timeout(10)
                ->post($this->apiUrl . 'GetTerminals', $requestData);

            if ($response->successful()) {
                $responseData = $response->json();

                if ($responseData['Success'] ?? false) {
                    Log::info("T-Bank connection test successful");
                    return true;
                }
            }

            Log::error("T-Bank connection test failed", [
                'status' => $response->status(),
                'response' => $response->json(),
            ]);

            return false;

        } catch (\Exception $e) {
            Log::error("Exception testing T-Bank connection", [
                'error' => $e->getMessage(),
            ]);
            return false;
        }
    }
}
