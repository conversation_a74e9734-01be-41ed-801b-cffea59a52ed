<?php

namespace App\Services;

use App\Models\User;
use App\Models\UserSubscription;
use App\Models\SubscriptionPlan;
use App\Models\ServerPool;
use App\Events\UserCreated;
use App\Events\UserSubscriptionRenewed;
use App\Events\UserSubscriptionExpired;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class UserSubscriptionService
{
    public function __construct(
        private ServerPoolService $serverPoolService
    ) {}

    /**
     * Create a new subscription for user.
     */
    public function createSubscription(User $user, SubscriptionPlan $plan, ?ServerPool $pool = null): ?UserSubscription
    {
        $callId = uniqid('call_');
        Log::info("UserSubscriptionService::createSubscription called", [
            'call_id' => $callId,
            'user_id' => $user->id,
            'plan_id' => $plan->id,
        ]);

        try {
            DB::beginTransaction();

            // Auto-assign to pool if not provided
            if (!$pool) {
                $pool = $this->serverPoolService->autoAssignUserToPool($user);
                if (!$pool) {
                    Log::error("No available pool for user subscription", [
                        'user_id' => $user->id,
                        'plan_id' => $plan->id,
                    ]);
                    return null;
                }
            }

            // Calculate subscription dates
            $startDate = now();
            $expiryDate = $plan->calculateExpiryDate($startDate);

            // Create subscription
            $subscription = UserSubscription::create([
                'user_id' => $user->id,
                'subscription_plan_id' => $plan->id,
                'server_pool_id' => $pool->id,
                'started_at' => $startDate,
                'expires_at' => $expiryDate,
                'traffic_limit_gb' => $plan->traffic_gb,
                'traffic_used_gb' => 0,
                'is_active' => true,
            ]);

            // Update user fields
            $user->update([
                'server_pool_id' => $pool->id,
                'subscription_plan_id' => $plan->id,
                'expiry_time' => $expiryDate,
                'total_gb' => $plan->traffic_gb ? $plan->traffic_gb * 1024 * 1024 * 1024 : 0, // Convert GB to bytes, 0 for unlimited
                'expired' => false,
            ]);

            DB::commit();

            Log::info("User subscription created", [
                'user_id' => $user->id,
                'subscription_id' => $subscription->id,
                'plan_id' => $plan->id,
                'plan_name' => $plan->name,
                'pool_id' => $pool->id,
                'expires_at' => $expiryDate->toDateTimeString(),
            ]);

            // Dispatch event for XUI client creation
            Log::info("Dispatching UserCreated event", [
                'call_id' => $callId,
                'user_id' => $user->id,
                'user_email' => $user->email,
            ]);
            UserCreated::dispatch($user);
            Log::info("UserCreated event dispatched", [
                'call_id' => $callId,
                'user_id' => $user->id,
            ]);

            return $subscription;

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error("Failed to create user subscription", [
                'user_id' => $user->id,
                'plan_id' => $plan->id,
                'error' => $e->getMessage(),
            ]);
            return null;
        }
    }

    /**
     * Renew/extend user subscription.
     */
    public function renewSubscription(User $user, SubscriptionPlan $plan): ?UserSubscription
    {
        try {
            DB::beginTransaction();

            // Deactivate current subscription
            $currentSubscription = $user->currentSubscription();
            if ($currentSubscription) {
                $currentSubscription->deactivate();
            }

            // Auto-assign to available pool (user might need to move to new pool)
            $pool = $this->serverPoolService->autoAssignUserToPool($user);
            if (!$pool) {
                Log::error("No available pool for subscription renewal", [
                    'user_id' => $user->id,
                    'plan_id' => $plan->id,
                ]);
                return null;
            }

            // Calculate new dates (extend from current expiry or now, whichever is later)
            $startDate = $user->expiry_time && $user->expiry_time->isFuture()
                ? $user->expiry_time
                : now();
            $expiryDate = $plan->calculateExpiryDate($startDate);

            // Create new subscription
            $subscription = UserSubscription::create([
                'user_id' => $user->id,
                'subscription_plan_id' => $plan->id,
                'server_pool_id' => $pool->id,
                'started_at' => now(),
                'expires_at' => $expiryDate,
                'traffic_limit_gb' => $plan->traffic_gb,
                'traffic_used_gb' => 0, // Reset traffic on renewal
                'is_active' => true,
            ]);

            // Update user fields
            $user->update([
                'server_pool_id' => $pool->id,
                'subscription_plan_id' => $plan->id,
                'expiry_time' => $expiryDate,
                'total_gb' => $plan->traffic_gb ? $plan->traffic_gb * 1024 * 1024 * 1024 : 0, // Convert GB to bytes, 0 for unlimited
                'used_gb' => 0, // Reset traffic
                'up_traffic' => 0,
                'down_traffic' => 0,
                'expired' => false,
                'traffic_reset_at' => now(),
            ]);

            DB::commit();

            Log::info("User subscription renewed", [
                'user_id' => $user->id,
                'old_subscription_id' => $currentSubscription?->id,
                'new_subscription_id' => $subscription->id,
                'plan_id' => $plan->id,
                'plan_name' => $plan->name,
                'pool_id' => $pool->id,
                'expires_at' => $expiryDate->toDateTimeString(),
            ]);

            // Dispatch event for XUI client update
            UserSubscriptionRenewed::dispatch($user, $subscription);

            return $subscription;

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error("Failed to renew user subscription", [
                'user_id' => $user->id,
                'plan_id' => $plan->id,
                'error' => $e->getMessage(),
            ]);
            return null;
        }
    }

    /**
     * Expire user subscription.
     */
    public function expireSubscription(User $user): bool
    {
        try {
            DB::beginTransaction();

            // Deactivate current subscription
            $currentSubscription = $user->currentSubscription();
            if ($currentSubscription) {
                $currentSubscription->deactivate();
            }

            // Remove user from pool
            $this->serverPoolService->removeUserFromPool($user);

            // Update user fields
            $user->update([
                'server_pool_id' => null,
                'expired' => true,
            ]);

            DB::commit();

            Log::info("User subscription expired", [
                'user_id' => $user->id,
                'subscription_id' => $currentSubscription?->id,
            ]);

            // Dispatch event for XUI client deletion
            UserSubscriptionExpired::dispatch($user);

            return true;

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error("Failed to expire user subscription", [
                'user_id' => $user->id,
                'error' => $e->getMessage(),
            ]);
            return false;
        }
    }

    /**
     * Update traffic usage for user.
     */
    public function updateTrafficUsage(User $user, int $upBytes, int $downBytes): bool
    {
        try {
            $totalBytes = $upBytes + $downBytes;

            // Update user traffic
            $user->increment('up_traffic', $upBytes);
            $user->increment('down_traffic', $downBytes);
            $user->increment('used_gb', $totalBytes);

            // Update current subscription traffic
            $currentSubscription = $user->currentSubscription();
            if ($currentSubscription) {
                $currentSubscription->addTrafficUsage($totalBytes);

                // Check if traffic limit exceeded
                if ($currentSubscription->isTrafficExceeded()) {
                    Log::warning("User traffic limit exceeded", [
                        'user_id' => $user->id,
                        'subscription_id' => $currentSubscription->id,
                        'used_gb' => $currentSubscription->traffic_used_gb,
                        'limit_gb' => $currentSubscription->traffic_limit_gb,
                    ]);
                }
            }

            return true;

        } catch (\Exception $e) {
            Log::error("Failed to update traffic usage", [
                'user_id' => $user->id,
                'up_bytes' => $upBytes,
                'down_bytes' => $downBytes,
                'error' => $e->getMessage(),
            ]);
            return false;
        }
    }

    /**
     * Get users with expired subscriptions.
     */
    public function getExpiredSubscriptions(): Collection
    {
        return UserSubscription::active()
            ->expired()
            ->with(['user', 'subscriptionPlan', 'serverPool'])
            ->get();
    }

    /**
     * Get users with subscriptions expiring soon.
     */
    public function getExpiringSoonSubscriptions(int $hours = 24): Collection
    {
        return UserSubscription::active()
            ->expiringSoon($hours)
            ->with(['user', 'subscriptionPlan', 'serverPool'])
            ->get();
    }

    /**
     * Process expired subscriptions.
     */
    public function processExpiredSubscriptions(): int
    {
        $expiredSubscriptions = $this->getExpiredSubscriptions();
        $processedCount = 0;

        foreach ($expiredSubscriptions as $subscription) {
            if ($this->expireSubscription($subscription->user)) {
                $processedCount++;
            }
        }

        Log::info("Processed expired subscriptions", [
            'total_expired' => $expiredSubscriptions->count(),
            'processed_count' => $processedCount,
        ]);

        return $processedCount;
    }

    /**
     * Get subscription statistics for user.
     */
    public function getUserSubscriptionStats(User $user): array
    {
        $currentSubscription = $user->currentSubscription();

        if (!$currentSubscription) {
            return [
                'has_subscription' => false,
                'is_expired' => true,
                'days_remaining' => 0,
                'traffic_used_gb' => 0,
                'traffic_limit_gb' => 0,
                'traffic_remaining_gb' => 0,
                'usage_percentage' => 0,
            ];
        }

        return [
            'has_subscription' => true,
            'is_expired' => $currentSubscription->isExpired(),
            'is_active' => $currentSubscription->isActiveAndValid(),
            'days_remaining' => $currentSubscription->getRemainingDays(),
            'traffic_used_gb' => $currentSubscription->traffic_used_gb,
            'traffic_limit_gb' => $currentSubscription->traffic_limit_gb,
            'traffic_remaining_gb' => $currentSubscription->getRemainingTrafficGb(),
            'usage_percentage' => $currentSubscription->getTrafficUsagePercentage(),
            'plan_name' => $currentSubscription->subscriptionPlan->name,
            'pool_name' => $currentSubscription->serverPool->name,
            'started_at' => $currentSubscription->started_at,
            'expires_at' => $currentSubscription->expires_at,
        ];
    }

    /**
     * Reset user traffic.
     */
    public function resetUserTraffic(User $user): bool
    {
        try {
            DB::beginTransaction();

            // Reset user traffic
            $user->update([
                'used_gb' => 0,
                'up_traffic' => 0,
                'down_traffic' => 0,
                'traffic_reset_at' => now(),
            ]);

            // Reset current subscription traffic
            $currentSubscription = $user->currentSubscription();
            if ($currentSubscription) {
                $currentSubscription->resetTrafficUsage();
            }

            DB::commit();

            Log::info("User traffic reset", [
                'user_id' => $user->id,
                'subscription_id' => $currentSubscription?->id,
            ]);

            return true;

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error("Failed to reset user traffic", [
                'user_id' => $user->id,
                'error' => $e->getMessage(),
            ]);
            return false;
        }
    }
}
