<?php

namespace App\Services;

use App\Models\XuiServer;
use App\Models\XuiInbound;
use App\Models\User;
use App\Services\HttpClientService;
use Illuminate\Http\Client\PendingRequest;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use GuzzleHttp\Cookie\CookieJar;

class XuiManagementService
{
    private array $httpClients = [];

    /**
     * Get or create HTTP client for a server.
     */
    private function getHttpClient(XuiServer $server): PendingRequest
    {
        $serverKey = $server->id;

        if (!isset($this->httpClients[$serverKey])) {
            $this->httpClients[$serverKey] = $this->createHttpClient($server);
        }

        return $this->httpClients[$serverKey];
    }

    /**
     * Create HTTP client for server.
     */
    private function createHttpClient(XuiServer $server): PendingRequest
    {
        $cookieJar = new CookieJar();

        // Add existing cookie if available
        if ($server->cookie) {
            $cookieJar = CookieJar::fromArray([
                '3x-ui' => $server->cookie,
                'lang' => 'en-EN',
            ], $server->host);
        }

        // Prepare options with cookies
        $options = [
            'cookies' => $cookieJar,
            'verify' => false,
            'timeout' => 30,
            'connect_timeout' => 10,
        ];

        // Create HTTP client with proper options
        return HttpClientService::create($options)
            ->withHeaders([
                'X-requested-with' => 'XMLHttpRequest',
            ])
            ->retry(2, 500)
            ->asForm()
            ->baseUrl($server->url);
    }

    /**
     * Authenticate with X-UI server.
     */
    public function authenticate(XuiServer $server): bool
    {
        // Check if we have a valid cookie (less than 3 hours old)
        if ($server->cookie && $server->last_login_at && $server->last_login_at->gt(now()->subHours(3))) {
            if ($this->checkAuth($server)) {
                return true;
            }
        }

        return $this->doLogin($server);
    }

    /**
     * Check if current authentication is valid.
     */
    private function checkAuth(XuiServer $server): bool
    {
        try {
            $httpClient = $this->getHttpClient($server);
            $response = $httpClient->post('/server/status');

            if ($response->successful() && $response->json('success') === true) {
                return true;
            }

            Log::info("Authentication check failed for server: {$server->name}");
            return false;
        } catch (\Exception $e) {
            Log::error("Exception during auth check: {$server->name}", [
                'error' => $e->getMessage(),
            ]);
            return false;
        }
    }

    /**
     * Perform login to X-UI server.
     */
    private function doLogin(XuiServer $server): bool
    {
        try {
            $httpClient = $this->createHttpClient($server);

            $response = $httpClient->post('/login', [
                'username' => $server->username,
                'password' => $server->password,
            ]);

            if ($response->successful() &&
                $response->json('success') === true &&
                $response->cookies()->getCookieByName('3x-ui')) {

                $cookie = $response->cookies()->getCookieByName('3x-ui')->getValue();

                // Update server with new cookie
                $server->update([
                    'cookie' => $cookie,
                    'last_login_at' => now(),
                ]);

                // Update the cached HTTP client
                $this->httpClients[$server->id] = $this->createHttpClient($server);

                Log::info("Successfully authenticated with X-UI server: {$server->name}");
                return true;
            }

            Log::error("Failed to authenticate with X-UI server: {$server->name}", [
                'status' => $response->status(),
                'response' => $response->json(),
            ]);

            return false;
        } catch (\Exception $e) {
            Log::error("Exception during X-UI authentication: {$server->name}", [
                'error' => $e->getMessage(),
            ]);

            return false;
        }
    }

    /**
     * Login to XUI server and store cookie (legacy method).
     */
    public function loginToServer(XuiServer $server): bool
    {
        return $this->authenticate($server);
    }

    /**
     * Sync server inbounds from API to database.
     */
    public function syncServerInbounds(XuiServer $server): bool
    {
        try {
            if (!$this->authenticate($server)) {
                return false;
            }

            $inbounds = $this->getInboundsList($server);
            if ($inbounds === null) {
                return false;
            }

            $synced = 0;
            $errors = 0;

            foreach ($inbounds as $inboundData) {
                try {
                    $this->syncInboundToDatabase($server, $inboundData);
                    $synced++;
                } catch (\Exception $e) {
                    $errors++;
                    Log::error("Failed to sync inbound to database", [
                        'server_id' => $server->id,
                        'inbound_id' => $inboundData['id'] ?? 'unknown',
                        'error' => $e->getMessage(),
                    ]);
                }
            }

            // Update server sync timestamp
            $server->update(['last_sync_at' => now()]);

            Log::info("Synced inbounds for server", [
                'server_id' => $server->id,
                'server_name' => $server->name,
                'synced' => $synced,
                'errors' => $errors,
                'total' => count($inbounds),
            ]);

            return $errors === 0;

        } catch (\Exception $e) {
            Log::error("Failed to sync server inbounds", [
                'server_id' => $server->id,
                'server_name' => $server->name,
                'error' => $e->getMessage(),
            ]);
            return false;
        }
    }

    /**
     * Sync single inbound to database.
     */
    private function syncInboundToDatabase(XuiServer $server, array $inboundData): void
    {
        try {
            // Decode settings if it's a JSON string
            $settings = $inboundData['settings'] ?? [];
            if (is_string($settings)) {
                $settings = json_decode($settings, true) ?? [];
            }

            // Decode streamSettings if present
            $streamSettings = null;
            if (isset($inboundData['streamSettings'])) {
                if (is_string($inboundData['streamSettings'])) {
                    $streamSettings = json_decode($inboundData['streamSettings'], true);
                } else {
                    $streamSettings = $inboundData['streamSettings'];
                }
            }

            // Store/update inbound
            XuiInbound::updateOrCreate(
                [
                    'xui_server_id' => $server->id,
                    'inbound_id' => $inboundData['id'],
                ],
                [
                    'up' => $inboundData['up'] ?? 0,
                    'down' => $inboundData['down'] ?? 0,
                    'total' => $inboundData['total'] ?? 0,
                    'remark' => $inboundData['remark'] ?? '',
                    'enable' => $inboundData['enable'] ?? false,
                    'expiry_time' => $inboundData['expiryTime'] ?? null,
                    'listen' => $inboundData['listen'] ?? '',
                    'port' => $inboundData['port'] ?? 0,
                    'protocol' => $inboundData['protocol'] ?? '',
                    'settings' => $settings,
                    'tag' => $inboundData['tag'] ?? '',
                    'sniffing' => $inboundData['sniffing'] ?? [],
                    'allocate' => $inboundData['allocate'] ?? [],
                    'stream_settings' => $streamSettings,
                    'raw_data' => $inboundData,
                ]
            );

        } catch (\Exception $e) {
            Log::error("Failed to sync inbound to database", [
                'server_id' => $server->id,
                'inbound_id' => $inboundData['id'] ?? 'unknown',
                'error' => $e->getMessage(),
            ]);
            throw $e;
        }
    }

    /**
     * Get server status from X-UI API
     */
    public function getServerStatus(XuiServer $server, bool $forceRefresh = false): ?array
    {
        try {
            if (!$this->authenticate($server)) {
                Log::warning("Failed to authenticate for server status: {$server->name}");
                return null;
            }

            $client = $this->getHttpClient($server);
            $response = $client->post('/server/status');

            if (!$response->successful()) {
                Log::error("Failed to get server status from X-UI server: {$server->name}", [
                    'status' => $response->status(),
                ]);
                return null;
            }

            $data = $response->json();

            if (!isset($data['success']) || !$data['success']) {
                Log::error("X-UI server returned unsuccessful status response: {$server->name}", [
                    'response' => $data,
                ]);
                return null;
            }

            return $data;
        } catch (\Exception $e) {
            Log::error("Exception while getting server status from X-UI server: {$server->name}", [
                'error' => $e->getMessage(),
            ]);

            return null;
        }
    }


    /**
     * Fetch settings from X-UI API and store in database.
     */
    public function fetchAndStoreSettings(XuiServer $server): ?array
    {
        try {
            if (!$this->authenticate($server)) {
                return null;
            }

            $client = $this->getHttpClient($server);
            $response = $client->post('/panel/setting/all');

            if (!$response->successful()) {
                Log::error("Failed to get settings from X-UI server: {$server->name}", [
                    'status' => $response->status(),
                ]);
                return null;
            }

            $data = $response->json();

            if (!isset($data['success']) || !$data['success']) {
                Log::error("X-UI server returned unsuccessful response: {$server->name}", [
                    'response' => $data,
                ]);
                return null;
            }

            // Store settings in database
            $server->update([
                'settings' => $data,
                'settings_updated_at' => now(),
            ]);

            Log::info("Updated settings for server: {$server->name}");
            return $data;
        } catch (\Exception $e) {
            Log::error("Exception while getting settings from X-UI server: {$server->name}", [
                'error' => $e->getMessage(),
            ]);

            return null;
        }
    }

    /**
     * Generate X25519 keys for inbound.
     */
    public function generateX25519Keys(XuiServer $server): ?array
    {
        try {
            $client = $this->getHttpClient($server);

            $response = $client->post('/server/getNewX25519Cert');

            if ($response->successful()) {
                $data = $response->json();

                if ($data['success'] ?? false) {
                    Log::info("Generated X25519 keys", [
                        'server_id' => $server->id,
                        'server_name' => $server->name,
                    ]);

                    return $data['obj'] ?? null;
                }
            }

            Log::error("Failed to generate X25519 keys", [
                'server_id' => $server->id,
                'server_name' => $server->name,
                'response' => $response->body(),
            ]);

            return null;

        } catch (\Exception $e) {
            Log::error("Exception generating X25519 keys", [
                'server_id' => $server->id,
                'error' => $e->getMessage(),
            ]);
            return null;
        }
    }

    /**
     * Create inbound on XUI server.
     */
    public function createInbound(XuiServer $server, array $inboundData): ?array
    {
        try {
            $client = $this->getHttpClient($server);

            $response = $client->asForm()->post('/panel/inbound/add', $inboundData);

            if ($response->successful()) {
                $data = $response->json();

                if ($data['success'] ?? false) {
                    Log::info("Created inbound on XUI server", [
                        'server_id' => $server->id,
                        'server_name' => $server->name,
                        'inbound_remark' => $inboundData['remark'] ?? 'Unknown',
                    ]);

                    return $data;
                }
            }

            Log::error("Failed to create inbound", [
                'server_id' => $server->id,
                'server_name' => $server->name,
                'response' => $response->body(),
            ]);

            return null;

        } catch (\Exception $e) {
            Log::error("Exception creating inbound", [
                'server_id' => $server->id,
                'error' => $e->getMessage(),
            ]);
            return null;
        }
    }

    /**
     * Update inbound on XUI server.
     */
    public function updateInbound(XuiServer $server, int $inboundId, array $data): bool
    {
        try {
            $client = $this->getHttpClient($server);

            $response = $client->asForm()->post("/panel/inbound/update/{$inboundId}", $data);

            if ($response->successful()) {
                $responseData = $response->json();

                if ($responseData['success'] ?? false) {
                    Log::info("Updated inbound on XUI server", [
                        'server_id' => $server->id,
                        'server_name' => $server->name,
                        'inbound_id' => $inboundId,
                    ]);

                    return true;
                }
            }

            Log::error("Failed to update inbound", [
                'server_id' => $server->id,
                'server_name' => $server->name,
                'inbound_id' => $inboundId,
                'response' => $response->body(),
            ]);

            return false;

        } catch (\Exception $e) {
            Log::error("Exception updating inbound", [
                'server_id' => $server->id,
                'inbound_id' => $inboundId,
                'error' => $e->getMessage(),
            ]);
            return false;
        }
    }

    /**
     * Delete inbound from XUI server.
     */
    public function deleteInbound(XuiServer $server, int $inboundId): bool
    {
        try {
            $client = $this->getHttpClient($server);

            $response = $client->post("/panel/inbound/del/{$inboundId}");

            if ($response->successful()) {
                $data = $response->json();

                if ($data['success'] ?? false) {
                    Log::info("Deleted inbound from XUI server", [
                        'server_id' => $server->id,
                        'server_name' => $server->name,
                        'inbound_id' => $inboundId,
                    ]);

                    return true;
                }
            }

            Log::error("Failed to delete inbound", [
                'server_id' => $server->id,
                'server_name' => $server->name,
                'inbound_id' => $inboundId,
                'response' => $response->body(),
            ]);

            return false;

        } catch (\Exception $e) {
            Log::error("Exception deleting inbound", [
                'server_id' => $server->id,
                'inbound_id' => $inboundId,
                'error' => $e->getMessage(),
            ]);
            return false;
        }
    }

    /**
     * Import inbound to XUI server (restore from backup).
     */
    public function importInbound(XuiServer $server, array $backupData): ?array
    {
        try {
            $client = $this->getHttpClient($server);

            $response = $client->asForm()->post('/panel/inbound/import', [
                'data' => json_encode($backupData)
            ]);

            if ($response->successful()) {
                $data = $response->json();

                if ($data['success'] ?? false) {
                    Log::info("Imported inbound to XUI server", [
                        'server_id' => $server->id,
                        'server_name' => $server->name,
                        'inbound_remark' => $backupData['remark'] ?? 'Unknown',
                    ]);

                    return $data;
                }
            }

            Log::error("Failed to import inbound", [
                'server_id' => $server->id,
                'server_name' => $server->name,
                'response' => $response->body(),
            ]);

            return null;

        } catch (\Exception $e) {
            Log::error("Exception importing inbound", [
                'server_id' => $server->id,
                'error' => $e->getMessage(),
            ]);
            return null;
        }
    }

    /**
     * Create client on XUI server.
     */
    public function createClient(XuiServer $server, int $inboundId, array $clientData): bool
    {
        try {
            $client = $this->getHttpClient($server);

            $response = $client->asForm()->post('/panel/inbound/addClient', [
                'id' => $inboundId,
                'settings' => json_encode(['clients' => [$clientData]])
            ]);

            if ($response->successful()) {
                $data = $response->json();

                if ($data['success'] ?? false) {
                    Log::info("Created client on XUI server", [
                        'server_id' => $server->id,
                        'server_name' => $server->name,
                        'inbound_id' => $inboundId,
                        'client_email' => $clientData['email'] ?? 'Unknown',
                        'client_id' => $clientData['id'] ?? 'Unknown',
                    ]);

                    return true;
                }
            }

            // Check if it's a duplicate error
            $responseBody = $response->body();
            if (str_contains($responseBody, 'Duplicate email')) {
                Log::info("Client already exists on XUI server (duplicate email)", [
                    'server_id' => $server->id,
                    'server_name' => $server->name,
                    'inbound_id' => $inboundId,
                    'client_email' => $clientData['email'] ?? 'Unknown',
                ]);
                return true; // Consider duplicate as success
            }

            Log::error("Failed to create client", [
                'server_id' => $server->id,
                'server_name' => $server->name,
                'inbound_id' => $inboundId,
                'client_email' => $clientData['email'] ?? 'Unknown',
                'response' => $responseBody,
            ]);

            return false;

        } catch (\Exception $e) {
            Log::error("Exception creating client", [
                'server_id' => $server->id,
                'inbound_id' => $inboundId,
                'client_email' => $clientData['email'] ?? 'Unknown',
                'error' => $e->getMessage(),
            ]);
            return false;
        }
    }

    /**
     * Update client on XUI server.
     */
    public function updateClient(XuiServer $server, int $inboundId, string $clientId, array $clientData): bool
    {
        try {
            $client = $this->getHttpClient($server);

            $response = $client->asForm()->post("/panel/inbound/updateClient/{$clientId}", [
                'id' => $inboundId,
                'settings' => json_encode(['clients' => [$clientData]])
            ]);

            if ($response->successful()) {
                $data = $response->json();

                if ($data['success'] ?? false) {
                    Log::info("Updated client on XUI server", [
                        'server_id' => $server->id,
                        'server_name' => $server->name,
                        'inbound_id' => $inboundId,
                        'client_id' => $clientId,
                        'client_email' => $clientData['email'] ?? 'Unknown',
                    ]);

                    return true;
                }
            }

            Log::error("Failed to update client", [
                'server_id' => $server->id,
                'server_name' => $server->name,
                'inbound_id' => $inboundId,
                'client_id' => $clientId,
                'response' => $response->body(),
            ]);

            return false;

        } catch (\Exception $e) {
            Log::error("Exception updating client", [
                'server_id' => $server->id,
                'inbound_id' => $inboundId,
                'client_id' => $clientId,
                'error' => $e->getMessage(),
            ]);
            return false;
        }
    }

    /**
     * Delete client from XUI server.
     */
    public function deleteClient(XuiServer $server, int $inboundId, string $clientId): bool
    {
        try {
            $client = $this->getHttpClient($server);

            $response = $client->post("/panel/inbound/{$inboundId}/delClient/{$clientId}");

            if ($response->successful()) {
                $data = $response->json();

                if ($data['success'] ?? false) {
                    Log::info("Deleted client from XUI server", [
                        'server_id' => $server->id,
                        'server_name' => $server->name,
                        'inbound_id' => $inboundId,
                        'client_id' => $clientId,
                    ]);

                    return true;
                }
            }

            Log::error("Failed to delete client", [
                'server_id' => $server->id,
                'server_name' => $server->name,
                'inbound_id' => $inboundId,
                'client_id' => $clientId,
                'response' => $response->body(),
            ]);

            return false;

        } catch (\Exception $e) {
            Log::error("Exception deleting client", [
                'server_id' => $server->id,
                'inbound_id' => $inboundId,
                'client_id' => $clientId,
                'error' => $e->getMessage(),
            ]);
            return false;
        }
    }

    /**
     * Reset client traffic on XUI server.
     */
    public function resetClientTraffic(XuiServer $server, int $inboundId, string $email): bool
    {
        try {
            $client = $this->getHttpClient($server);

            $response = $client->post("/panel/inbound/{$inboundId}/resetClientTraffic/{$email}");

            if ($response->successful()) {
                $data = $response->json();

                if ($data['success'] ?? false) {
                    Log::info("Reset client traffic on XUI server", [
                        'server_id' => $server->id,
                        'server_name' => $server->name,
                        'inbound_id' => $inboundId,
                        'client_email' => $email,
                    ]);

                    return true;
                }
            }

            Log::error("Failed to reset client traffic", [
                'server_id' => $server->id,
                'server_name' => $server->name,
                'inbound_id' => $inboundId,
                'client_email' => $email,
                'response' => $response->body(),
            ]);

            return false;

        } catch (\Exception $e) {
            Log::error("Exception resetting client traffic", [
                'server_id' => $server->id,
                'inbound_id' => $inboundId,
                'client_email' => $email,
                'error' => $e->getMessage(),
            ]);
            return false;
        }
    }

    /**
     * Get inbounds list from XUI server.
     */
    public function getInboundsList(XuiServer $server): ?array
    {
        try {
            if (!$this->authenticate($server)) {
                return null;
            }

            $client = $this->getHttpClient($server);

            $response = $client->post('/panel/inbound/list');

            if ($response->successful()) {
                $data = $response->json();

                if ($data['success'] ?? false) {
                    Log::debug("Retrieved inbounds list from XUI server", [
                        'server_id' => $server->id,
                        'server_name' => $server->name,
                        'inbounds_count' => count($data['obj'] ?? []),
                    ]);

                    return $data['obj'] ?? [];
                }
            }

            Log::error("Failed to get inbounds list", [
                'server_id' => $server->id,
                'server_name' => $server->name,
                'response' => $response->body(),
            ]);

            return null;

        } catch (\Exception $e) {
            Log::error("Exception getting inbounds list", [
                'server_id' => $server->id,
                'error' => $e->getMessage(),
            ]);
            return null;
        }
    }

    /**
     * Get online clients from XUI server.
     */
    public function getOnlineClients(XuiServer $server): ?array
    {
        try {
            $client = $this->getHttpClient($server);

            $response = $client->post('/panel/inbound/onlines');

            if ($response->successful()) {
                $data = $response->json();

                if ($data['success'] ?? false) {
                    Log::debug("Retrieved online clients from XUI server", [
                        'server_id' => $server->id,
                        'server_name' => $server->name,
                        'online_count' => count($data['obj'] ?? []),
                    ]);

                    return $data['obj'] ?? [];
                }
            }

            Log::error("Failed to get online clients", [
                'server_id' => $server->id,
                'server_name' => $server->name,
                'response' => $response->body(),
            ]);

            return null;

        } catch (\Exception $e) {
            Log::error("Exception getting online clients", [
                'server_id' => $server->id,
                'error' => $e->getMessage(),
            ]);
            return null;
        }
    }

    /**
     * Create XUI clients for user on all servers in their pool.
     */
    public function createUserClientsOnPool(User $user): bool
    {
        if (!$user->serverPool) {
            Log::warning("User has no server pool assigned", [
                'user_id' => $user->id,
            ]);
            return false;
        }

        $servers = $user->serverPool->servers()->where('is_active', true)->get();
        $successCount = 0;
        $totalServers = $servers->count();

        foreach ($servers as $server) {
            $inbounds = $server->xuiInbounds()->where('enable', true)->get();

            foreach ($inbounds as $inbound) {
                $clientData = $this->buildClientData($user, $inbound);

                if ($this->createClient($server, $inbound->inbound_id, $clientData)) {
                    $successCount++;
                }
            }
        }

        Log::info("Created XUI clients for user", [
            'user_id' => $user->id,
            'pool_id' => $user->serverPool->id,
            'total_servers' => $totalServers,
            'success_count' => $successCount,
        ]);

        return $successCount > 0;
    }

    /**
     * Update XUI clients for user on all servers in their pool.
     */
    public function updateUserClientsOnPool(User $user): bool
    {
        if (!$user->serverPool) {
            Log::warning("User has no server pool assigned", [
                'user_id' => $user->id,
            ]);
            return false;
        }

        $servers = $user->serverPool->servers()->where('is_active', true)->get();
        $successCount = 0;

        foreach ($servers as $server) {
            $inbounds = $server->xuiInbounds()->where('enable', true)->get();

            foreach ($inbounds as $inbound) {
                $clientData = $this->buildClientData($user, $inbound);

                if ($this->updateClient($server, $inbound->inbound_id, $user->uuid, $clientData)) {
                    $successCount++;
                }
            }
        }

        Log::info("Updated XUI clients for user", [
            'user_id' => $user->id,
            'pool_id' => $user->serverPool->id,
            'success_count' => $successCount,
        ]);

        return $successCount > 0;
    }

    /**
     * Delete XUI clients for user from all servers.
     */
    public function deleteUserClientsFromAllServers(User $user): bool
    {
        $servers = XuiServer::active()->get();
        $successCount = 0;

        foreach ($servers as $server) {
            $inbounds = $server->xuiInbounds()->where('enable', true)->get();

            foreach ($inbounds as $inbound) {
                if ($this->deleteClient($server, $inbound->inbound_id, $user->uuid)) {
                    $successCount++;
                }
            }
        }

        Log::info("Deleted XUI clients for user", [
            'user_id' => $user->id,
            'success_count' => $successCount,
        ]);

        return $successCount > 0;
    }

    /**
     * Build client data array for XUI API.
     */
    private function buildClientData(User $user, XuiInbound $inbound): array
    {
        $expiryTime = $user->expiry_time ? $user->expiry_time->timestamp * 1000 : -86400000; // Convert to milliseconds or 1 day "Start After First Use"
        $totalGB = $user->total_gb ?? 0;
        $clientEmail = $user->generateClientEmail($inbound->inbound_id);

        return [
            'id' => $user->uuid,
            'flow' => 'xtls-rprx-vision',
            'email' => $clientEmail,
            'limitIp' => 0,
            'totalGB' => $totalGB,
            'expiryTime' => $expiryTime,
            'enable' => true,
            'tgId' => $user->tg_id ?? '',
            'subId' => Str::random(16),
            'comment' => $user->comment ?? '',
            'reset' => 0,
        ];
    }

    /**
     * Sync online clients data and update user last_online_at.
     */
    public function syncOnlineClients(XuiServer $server): bool
    {
        try {
            $onlineClients = $this->getOnlineClients($server);

            if ($onlineClients === null) {
                return false;
            }

            // Store online clients data
            $server->updateOnlineClients($onlineClients);

            // Update last_online_at for users
            if (!empty($onlineClients)) {
                $this->updateUsersLastOnline($onlineClients);
            }

            Log::info("Synced online clients for server", [
                'server_id' => $server->id,
                'server_name' => $server->name,
                'online_clients_count' => count($onlineClients),
            ]);

            return true;

        } catch (\Exception $e) {
            Log::error("Failed to sync online clients", [
                'server_id' => $server->id,
                'server_name' => $server->name,
                'error' => $e->getMessage(),
            ]);
            return false;
        }
    }

    /**
     * Update last_online_at for users based on online clients.
     */
    private function updateUsersLastOnline(array $onlineClients): void
    {
        try {

            $emailsOnline = array_map(function ($client) {
                return User::extractUserEmailFromClientEmail($client['email'] ?? '');
            }, $onlineClients);

            // Get all XUI clients that match the online client emails
            $users = User::whereIn('email', $emailsOnline)
                ->with('user')
                ->get();

            $updatedUsers = 0;
            $now = now();

            foreach ($users as $user) {
                $user->update(['last_online_at' => $now]);
                $updatedUsers++;
            }

            Log::debug("Updated last_online_at for users", [
                'online_clients' => count($emailsOnline),
                'updated_users' => $updatedUsers,
            ]);

        } catch (\Exception $e) {
            Log::error("Failed to update users last_online_at", [
                'error' => $e->getMessage(),
            ]);
        }
    }
}
