<?php

namespace App\Services;

use App\Models\User;
use App\Models\Payment;
use App\Events\ReferralRewardEarned;
use Illuminate\Support\Facades\Log;

class ReferralService
{
    /**
     * Default referral reward percentage.
     */
    private float $defaultRewardPercentage = 0.10; // 10%

    /**
     * Process referral reward for payment.
     */
    public function processReferralReward(Payment $payment): bool
    {
        try {
            $order = $payment->order;
            $user = $order->user;

            // Check if user has a referrer
            $referrer = $this->getUserReferrer($user);
            if (!$referrer) {
                Log::debug("No referrer found for user", [
                    'user_id' => $user->id,
                ]);
                return false;
            }

            // Calculate reward amount
            $rewardAmount = $this->calculateRewardAmount($payment);
            if ($rewardAmount <= 0) {
                Log::debug("No reward amount calculated", [
                    'user_id' => $user->id,
                    'referrer_id' => $referrer->id,
                    'payment_amount' => $payment->getAmount(),
                ]);
                return false;
            }

            // Dispatch referral reward event
            ReferralRewardEarned::dispatch($referrer, $user, $payment, $rewardAmount);

            Log::info("Referral reward processed", [
                'user_id' => $user->id,
                'referrer_id' => $referrer->id,
                'payment_id' => $payment->id,
                'reward_amount' => $rewardAmount,
            ]);

            return true;

        } catch (\Exception $e) {
            Log::error("Exception processing referral reward", [
                'payment_id' => $payment->id,
                'error' => $e->getMessage(),
            ]);
            return false;
        }
    }

    /**
     * Get user's referrer.
     */
    public function getUserReferrer(User $user): ?User
    {
        // Check if user has referrer_id
        if ($user->referrer_id) {
            return User::find($user->referrer_id);
        }

        // Check if user has referrer info in additional_data
        if ($user->additional_data && isset($user->additional_data['referrer_contact_id'])) {
            $referrerContactId = $user->additional_data['referrer_contact_id'];

            return User::where('source', 'leadteh')
                ->whereJsonContains('additional_data->contact_id', $referrerContactId)
                ->first();
        }

        return null;
    }

    /**
     * Calculate reward amount for payment.
     */
    public function calculateRewardAmount(Payment $payment): float
    {
        $paymentAmount = $payment->getAmount();

        // Get reward percentage from subscription plan or use default
        $order = $payment->order;
        $plan = $order->subscriptionPlan;

        $rewardPercentage = $plan->referral_reward_percentage ?? $this->defaultRewardPercentage;

        return round($paymentAmount * $rewardPercentage, 2);
    }

    /**
     * Set referrer for user.
     */
    public function setUserReferrer(User $user, User $referrer): bool
    {
        try {
            // Don't allow self-referral
            if ($user->id === $referrer->id) {
                Log::warning("Attempted self-referral", [
                    'user_id' => $user->id,
                ]);
                return false;
            }

            // Don't change referrer if already set
            if ($user->referrer_id) {
                Log::warning("User already has referrer", [
                    'user_id' => $user->id,
                    'existing_referrer_id' => $user->referrer_id,
                    'new_referrer_id' => $referrer->id,
                ]);
                return false;
            }

            $user->update(['referrer_id' => $referrer->id]);

            Log::info("Referrer set for user", [
                'user_id' => $user->id,
                'referrer_id' => $referrer->id,
            ]);

            return true;

        } catch (\Exception $e) {
            Log::error("Exception setting user referrer", [
                'user_id' => $user->id,
                'referrer_id' => $referrer->id,
                'error' => $e->getMessage(),
            ]);
            return false;
        }
    }

    /**
     * Set referrer by contact ID (for LeadTeh integration).
     */
    public function setUserReferrerByContactId(User $user, string $referrerContactId): bool
    {
        try {
            // Find referrer by contact ID
            $referrer = User::where('source', 'leadteh')
                ->whereJsonContains('additional_data->contact_id', $referrerContactId)
                ->first();

            if (!$referrer) {
                // Store referrer contact ID in additional_data for later resolution
                $additionalData = $user->additional_data ?? [];
                $additionalData['referrer_contact_id'] = $referrerContactId;

                $user->update(['additional_data' => $additionalData]);

                Log::info("Referrer contact ID stored for later resolution", [
                    'user_id' => $user->id,
                    'referrer_contact_id' => $referrerContactId,
                ]);

                return true;
            }

            return $this->setUserReferrer($user, $referrer);

        } catch (\Exception $e) {
            Log::error("Exception setting user referrer by contact ID", [
                'user_id' => $user->id,
                'referrer_contact_id' => $referrerContactId,
                'error' => $e->getMessage(),
            ]);
            return false;
        }
    }

    /**
     * Get referral statistics for user.
     */
    public function getUserReferralStats(User $user): array
    {
        try {
            // Get referred users
            $referredUsers = User::where('referrer_id', $user->id)->get();

            // Calculate total rewards (this would need a referral_rewards table in production)
            $totalRewards = 0;
            $totalReferrals = $referredUsers->count();
            $activeReferrals = $referredUsers->filter(function ($referredUser) {
                return $referredUser->currentSubscription()?->isActiveAndValid();
            })->count();

            return [
                'total_referrals' => $totalReferrals,
                'active_referrals' => $activeReferrals,
                'total_rewards' => $totalRewards,
                'referred_users' => $referredUsers->map(function ($referredUser) {
                    return [
                        'id' => $referredUser->id,
                        'email' => $referredUser->email,
                        'name' => $referredUser->name,
                        'created_at' => $referredUser->created_at,
                        'has_active_subscription' => (bool) $referredUser->currentSubscription()?->isActiveAndValid(),
                    ];
                })->toArray(),
            ];

        } catch (\Exception $e) {
            Log::error("Exception getting user referral stats", [
                'user_id' => $user->id,
                'error' => $e->getMessage(),
            ]);

            return [
                'total_referrals' => 0,
                'active_referrals' => 0,
                'total_rewards' => 0,
                'referred_users' => [],
            ];
        }
    }

    /**
     * Generate referral link for user.
     */
    public function generateReferralLink(User $user): string
    {
        $baseUrl = config('app.url');
        $referralCode = $this->generateReferralCode($user);

        return "{$baseUrl}/ref/{$referralCode}";
    }

    /**
     * Generate referral code for user.
     */
    public function generateReferralCode(User $user): string
    {
        // Use user ID + some salt for referral code
        return base64_encode($user->id . ':' . config('app.key'));
    }

    /**
     * Resolve referral code to user.
     */
    public function resolveReferralCode(string $referralCode): ?User
    {
        try {
            $decoded = base64_decode($referralCode);
            $parts = explode(':', $decoded);

            if (count($parts) !== 2) {
                return null;
            }

            $userId = (int) $parts[0];
            $salt = $parts[1];

            // Verify salt
            if ($salt !== config('app.key')) {
                return null;
            }

            return User::find($userId);

        } catch (\Exception $e) {
            Log::error("Exception resolving referral code", [
                'referral_code' => $referralCode,
                'error' => $e->getMessage(),
            ]);
            return null;
        }
    }

    /**
     * Process referral from link.
     */
    public function processReferralFromLink(string $referralCode, User $newUser): bool
    {
        $referrer = $this->resolveReferralCode($referralCode);

        if (!$referrer) {
            Log::warning("Invalid referral code", [
                'referral_code' => $referralCode,
                'new_user_id' => $newUser->id,
            ]);
            return false;
        }

        return $this->setUserReferrer($newUser, $referrer);
    }
}
