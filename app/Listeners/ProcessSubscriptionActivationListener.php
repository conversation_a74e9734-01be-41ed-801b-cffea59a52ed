<?php

namespace App\Listeners;

use App\Events\PaymentReceived;
use App\Events\UserSubscriptionRenewed;
use App\Services\UserSubscriptionService;
use App\Services\ReferralService;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Log;

class ProcessSubscriptionActivationListener implements ShouldQueue
{
    use InteractsWithQueue;

    /**
     * The number of times the job may be attempted.
     */
    public int $tries = 3;

    /**
     * The number of seconds to wait before retrying the job.
     */
    public int $backoff = 30;

    /**
     * Create the event listener.
     */
    public function __construct(
        private UserSubscriptionService $userSubscriptionService,
        private ReferralService $referralService
    ) {}

    /**
     * Handle the event.
     */
    public function handle(PaymentReceived $event): void
    {
        $payment = $event->payment;
        $order = $event->order;
        $user = $order->user;
        $subscriptionPlan = $order->subscriptionPlan;

        try {
            // Create or renew subscription
            $subscription = $this->userSubscriptionService->renewSubscription($user, $subscriptionPlan);

            if ($subscription) {
                // Mark order as paid
                $order->markAsPaid();

                Log::info("Successfully activated subscription after payment", [
                    'user_id' => $user->id,
                    'order_id' => $order->id,
                    'payment_id' => $payment->id,
                    'subscription_id' => $subscription->id,
                    'plan_name' => $subscriptionPlan->name,
                ]);

                // Dispatch event to update XUI clients
                UserSubscriptionRenewed::dispatch($user, $subscription);

                // Process referral reward
                $this->referralService->processReferralReward($payment);

            } else {
                Log::error("Failed to activate subscription after payment", [
                    'user_id' => $user->id,
                    'order_id' => $order->id,
                    'payment_id' => $payment->id,
                    'plan_name' => $subscriptionPlan->name,
                ]);

                // Mark order as failed
                $order->markAsFailed();
            }

        } catch (\Exception $e) {
            Log::error("Exception activating subscription after payment", [
                'user_id' => $user->id,
                'order_id' => $order->id,
                'payment_id' => $payment->id,
                'error' => $e->getMessage(),
            ]);

            // Mark order as failed
            $order->markAsFailed();

            // Re-throw to trigger job retry
            throw $e;
        }
    }

    /**
     * Handle a job failure.
     */
    public function failed(PaymentReceived $event, \Throwable $exception): void
    {
        $payment = $event->payment;
        $order = $event->order;

        Log::error("Failed to activate subscription after retries", [
            'user_id' => $order->user->id,
            'order_id' => $order->id,
            'payment_id' => $payment->id,
            'error' => $exception->getMessage(),
        ]);

        // Mark order as failed
        $order->markAsFailed();
    }
}
