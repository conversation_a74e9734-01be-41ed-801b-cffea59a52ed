<?php

namespace App\Listeners;

use App\Events\UserSubscriptionRenewed;
use App\Services\SubscriptionService;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Log;

class UpdateXuiClientsListener implements ShouldQueue
{
    use InteractsWithQueue;

    /**
     * The number of times the job may be attempted.
     */
    public int $tries = 3;

    /**
     * The number of seconds to wait before retrying the job.
     */
    public int $backoff = 30;

    /**
     * Create the event listener.
     */
    public function __construct(
        private SubscriptionService $subscriptionService
    ) {}

    /**
     * Handle the event.
     */
    public function handle(UserSubscriptionRenewed $event): void
    {
        $user = $event->user;
        $subscription = $event->subscription;

        // Check if user has a pool assigned
        if (!$user->serverPool) {
            Log::warning("User has no server pool assigned for subscription renewal", [
                'user_id' => $user->id,
                'subscription_id' => $subscription->id,
            ]);
            return;
        }

        try {
            // Ensure user has clients on all servers in their pool
            $success = $this->subscriptionService->ensureUserClientsOnServers($user);

            if ($success) {
                Log::info("Successfully ensured XUI clients for renewed subscription", [
                    'user_id' => $user->id,
                    'subscription_id' => $subscription->id,
                    'pool_id' => $user->serverPool?->id,
                ]);
            } else {
                throw new \Exception("Failed to ensure XUI clients for renewed subscription");
            }

        } catch (\Exception $e) {
            Log::error("Exception updating XUI clients for renewed subscription", [
                'user_id' => $user->id,
                'subscription_id' => $subscription->id,
                'error' => $e->getMessage(),
            ]);

            // Re-throw to trigger job retry
            throw $e;
        }
    }

    /**
     * Handle a job failure.
     */
    public function failed(UserSubscriptionRenewed $event, \Throwable $exception): void
    {
        Log::error("Failed to update XUI clients after retries", [
            'user_id' => $event->user->id,
            'subscription_id' => $event->subscription->id,
            'error' => $exception->getMessage(),
        ]);
    }
}
