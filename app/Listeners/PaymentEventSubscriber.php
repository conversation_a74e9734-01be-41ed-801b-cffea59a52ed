<?php

namespace App\Listeners;

use App\Events\PaymentReceived;
use App\Events\ReferralRewardEarned;
use Illuminate\Events\Dispatcher;
use Illuminate\Support\Facades\Log;

class PaymentEventSubscriber
{
    /**
     * Handle payment received events.
     */
    public function handlePaymentReceived(PaymentReceived $event): void
    {
        Log::info('PaymentEventSubscriber: Payment received event received', [
            'payment_id' => $event->payment->id,
            'order_id' => $event->order->id,
            'amount' => $event->payment->getAmountRubles(),
        ]);
    }

    /**
     * Handle referral reward earned events.
     */
    public function handleReferralRewardEarned(ReferralRewardEarned $event): void
    {
        Log::info('PaymentEventSubscriber: Referral reward earned event received', [
            'referrer_id' => $event->referrer->id,
            'referred_id' => $event->referred->id,
            'payment_id' => $event->payment->id,
            'reward_amount' => $event->rewardAmount,
        ]);
    }

    /**
     * Register the listeners for the subscriber.
     */
    public function subscribe(Dispatcher $events): void
    {
        $events->listen(
            PaymentReceived::class,
            [PaymentEventSubscriber::class, 'handlePaymentReceived']
        );

        $events->listen(
            ReferralRewardEarned::class,
            [PaymentEventSubscriber::class, 'handleReferralRewardEarned']
        );
    }
}
