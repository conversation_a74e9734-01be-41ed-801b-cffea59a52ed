<?php

namespace App\Listeners;

use App\Events\ReferralRewardEarned;
use App\Services\LeadtehIntegrationService;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Log;

class ProcessReferralRewardListener implements ShouldQueue
{
    use InteractsWithQueue;

    /**
     * The number of times the job may be attempted.
     */
    public int $tries = 3;

    /**
     * The number of seconds to wait before retrying the job.
     */
    public int $backoff = 30;

    /**
     * Create the event listener.
     */
    public function __construct(
        private LeadtehIntegrationService $leadtehService
    ) {}

    /**
     * Handle the event.
     */
    public function handle(ReferralRewardEarned $event): void
    {
        $referrer = $event->referrer;
        $referred = $event->referred;
        $payment = $event->payment;
        $rewardAmount = $event->rewardAmount;

        // Only process if referrer is from LeadTeh
        if ($referrer->source !== 'leadteh' || !$referrer->additional_data) {
            Log::debug("Skipping referral reward - referrer not from LeadTeh", [
                'referrer_id' => $referrer->id,
                'referred_id' => $referred->id,
                'source' => $referrer->source,
            ]);
            return;
        }

        $contactId = $referrer->additional_data['contact_id'] ?? null;
        if (!$contactId) {
            Log::warning("LeadTeh referrer missing contact_id", [
                'referrer_id' => $referrer->id,
                'referred_id' => $referred->id,
                'additional_data' => $referrer->additional_data,
            ]);
            return;
        }

        try {
            $success = $this->leadtehService->sendReferralReward($contactId, $rewardAmount);

            if ($success) {
                Log::info("Referral reward sent to LeadTeh", [
                    'referrer_id' => $referrer->id,
                    'referred_id' => $referred->id,
                    'contact_id' => $contactId,
                    'reward_amount' => $rewardAmount,
                    'payment_id' => $payment->id,
                ]);
            } else {
                Log::error("Failed to send referral reward to LeadTeh", [
                    'referrer_id' => $referrer->id,
                    'referred_id' => $referred->id,
                    'contact_id' => $contactId,
                    'reward_amount' => $rewardAmount,
                    'payment_id' => $payment->id,
                ]);
            }

        } catch (\Exception $e) {
            Log::error("Exception processing referral reward", [
                'referrer_id' => $referrer->id,
                'referred_id' => $referred->id,
                'contact_id' => $contactId,
                'reward_amount' => $rewardAmount,
                'error' => $e->getMessage(),
            ]);

            // Re-throw to trigger job retry
            throw $e;
        }
    }

    /**
     * Handle a job failure.
     */
    public function failed(ReferralRewardEarned $event, \Throwable $exception): void
    {
        $referrer = $event->referrer;
        $contactId = $referrer->additional_data['contact_id'] ?? 'unknown';

        Log::error("Failed to process referral reward after retries", [
            'referrer_id' => $referrer->id,
            'referred_id' => $event->referred->id,
            'contact_id' => $contactId,
            'reward_amount' => $event->rewardAmount,
            'error' => $exception->getMessage(),
        ]);
    }
}
