<?php

namespace App\Listeners;

use App\Events\UserCreated;
use App\Events\UserSubscriptionRenewed;
use App\Events\UserSubscriptionExpired;
use Illuminate\Events\Dispatcher;
use Illuminate\Support\Facades\Log;

class UserEventSubscriber
{
    /**
     * Handle user created events.
     */
    public function handleUserCreated(UserCreated $event): void
    {
        Log::info('UserEventSubscriber: User created event received', [
            'user_id' => $event->user->id,
            'user_email' => $event->user->email,
        ]);
    }

    /**
     * Handle user subscription renewed events.
     */
    public function handleUserSubscriptionRenewed(UserSubscriptionRenewed $event): void
    {
        Log::info('UserEventSubscriber: User subscription renewed event received', [
            'user_id' => $event->user->id,
            'subscription_id' => $event->subscription->id,
        ]);
    }

    /**
     * Handle user subscription expired events.
     */
    public function handleUserSubscriptionExpired(UserSubscriptionExpired $event): void
    {
        Log::info('UserEventSubscriber: User subscription expired event received', [
            'user_id' => $event->user->id,
        ]);
    }

    /**
     * Register the listeners for the subscriber.
     */
    public function subscribe(Dispatcher $events): void
    {
        $events->listen(
            UserCreated::class,
            [UserEventSubscriber::class, 'handleUserCreated']
        );

        $events->listen(
            UserSubscriptionRenewed::class,
            [UserEventSubscriber::class, 'handleUserSubscriptionRenewed']
        );

        $events->listen(
            UserSubscriptionExpired::class,
            [UserEventSubscriber::class, 'handleUserSubscriptionExpired']
        );
    }
}
