<?php

namespace App\Listeners;

use App\Events\UserCreated;
use App\Services\XuiManagementService;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Log;

class CreateXuiClientsListener implements ShouldQueue
{
    use InteractsWithQueue;

    /**
     * The number of times the job may be attempted.
     */
    public int $tries = 3;

    /**
     * The number of seconds to wait before retrying the job.
     */
    public int $backoff = 30;

    /**
     * Create the event listener.
     */
    public function __construct(
        private XuiManagementService $xuiManagementService
    ) {}

    /**
     * Handle the event.
     */
    public function handle(UserCreated $event): void
    {
        $user = $event->user;
        $handlerId = uniqid('handler_');

        Log::info("CreateXuiClientsListener: Starting to handle UserCreated event", [
            'handler_id' => $handlerId,
            'user_id' => $user->id,
            'user_email' => $user->email,
            'listener_class' => static::class,
        ]);

        $user->load(['serverPool', 'userSubscriptions']);

        $currentSubscription = $user->currentSubscription();

        // Only create clients if user has active subscription and is assigned to a pool
        if (! $user->serverPool || ! $currentSubscription->isActiveAndValid()) {
            Log::info("Skipping XUI client creation - no active subscription or pool", [
                'handler_id' => $handlerId,
                'user_id' => $user->id,
                'has_pool' => (bool) $user->serverPool,
                'has_subscription' => (bool) $user->currentSubscription(),
            ]);
            return;
        }

        try {
            $success = $this->xuiManagementService->createUserClientsOnPool($user);

            if ($success) {
                Log::info("Successfully created XUI clients for new user", [
                    'handler_id' => $handlerId,
                    'user_id' => $user->id,
                    'user_email' => $user->email,
                    'pool_id' => $user->serverPool->id,
                ]);
            } else {
                Log::error("Failed to create XUI clients for new user", [
                    'handler_id' => $handlerId,
                    'user_id' => $user->id,
                    'user_email' => $user->email,
                    'pool_id' => $user->serverPool->id,
                ]);
            }

        } catch (\Exception $e) {
            Log::error("Exception creating XUI clients for new user", [
                'user_id' => $user->id,
                'user_email' => $user->email,
                'error' => $e->getMessage(),
            ]);

            // Re-throw to trigger job retry
            throw $e;
        }
    }

    /**
     * Handle a job failure.
     */
    public function failed(UserCreated $event, \Throwable $exception): void
    {
        Log::error("Failed to create XUI clients after retries", [
            'user_id' => $event->user->id,
            'user_email' => $event->user->email,
            'error' => $exception->getMessage(),
        ]);
    }
}
