<?php

namespace App\Listeners;

use App\Events\UserDeleted;
use App\Events\UserSubscriptionExpired;
use App\Services\XuiManagementService;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Log;

class DeleteXuiClientsListener implements ShouldQueue
{
    use InteractsWithQueue;

    /**
     * The number of times the job may be attempted.
     */
    public int $tries = 3;

    /**
     * The number of seconds to wait before retrying the job.
     */
    public int $backoff = 30;

    /**
     * Create the event listener.
     */
    public function __construct(
        private XuiManagementService $xuiManagementService
    ) {}

    /**
     * Handle the event.
     */
    public function handle(UserDeleted | UserSubscriptionExpired $event): void
    {
        $user = $event->user;

        try {
            $success = $this->xuiManagementService->deleteUserClientsFromAllServers($user);

            if ($success) {
                Log::info("Successfully deleted XUI clients", [
                    'user_id' => $user->id,
                    'user_email' => $user->email,
                ]);
            } else {
                Log::warning("No XUI clients found to delete", [
                    'user_id' => $user->id,
                    'user_email' => $user->email,
                ]);
            }

        } catch (\Exception $e) {
            Log::error("Exception deleting XUI clients", [
                'user_id' => $user->id,
                'user_email' => $user->email,
                'error' => $e->getMessage(),
            ]);

            // Re-throw to trigger job retry
            throw $e;
        }
    }

    /**
     * Handle a job failure.
     */
    public function failed(UserSubscriptionExpired $event, \Throwable $exception): void
    {
        Log::error("Failed to delete XUI clients after retries", [
            'user_id' => $event->user->id,
            'user_email' => $event->user->email,
            'error' => $exception->getMessage(),
        ]);
    }
}
