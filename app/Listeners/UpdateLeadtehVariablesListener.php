<?php

namespace App\Listeners;

use App\Events\PaymentReceived;
use App\Services\LeadtehIntegrationService;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Log;

class UpdateLeadtehVariablesListener implements ShouldQueue
{
    use InteractsWithQueue;

    /**
     * The number of times the job may be attempted.
     */
    public int $tries = 3;

    /**
     * The number of seconds to wait before retrying the job.
     */
    public int $backoff = 30;

    /**
     * Create the event listener.
     */
    public function __construct(
        private LeadtehIntegrationService $leadtehService
    ) {}

    /**
     * Handle the event.
     */
    public function handle(PaymentReceived $event): void
    {
        $payment = $event->payment;
        $order = $event->order;
        $user = $order->user;

        // Only process if user is from LeadTeh
        if ($user->source !== 'leadteh' || !$user->additional_data) {
            Log::debug("Skipping LeadTeh variables update - user not from LeadTeh", [
                'user_id' => $user->id,
                'source' => $user->source,
            ]);
            return;
        }

        $contactId = $user->additional_data['contact_id'] ?? null;
        if (!$contactId) {
            Log::warning("LeadTeh user missing contact_id", [
                'user_id' => $user->id,
                'additional_data' => $user->additional_data,
            ]);
            return;
        }

        try {
            // Get current subscription for variables
            $currentSubscription = $user->currentSubscription();

            if (!$currentSubscription) {
                Log::warning("User has no current subscription for LeadTeh update", [
                    'user_id' => $user->id,
                    'contact_id' => $contactId,
                ]);
                return;
            }

            // Update subscription status in LeadTeh
            $success = $this->leadtehService->updateSubscriptionStatus(
                $contactId,
                'active',
                $currentSubscription
            );

            if ($success) {
                Log::info("LeadTeh variables updated successfully", [
                    'user_id' => $user->id,
                    'contact_id' => $contactId,
                    'payment_id' => $payment->id,
                ]);
            } else {
                Log::warning("Failed to update LeadTeh variables", [
                    'user_id' => $user->id,
                    'contact_id' => $contactId,
                    'payment_id' => $payment->id,
                ]);
            }

        } catch (\Exception $e) {
            Log::error("Exception updating LeadTeh variables", [
                'user_id' => $user->id,
                'contact_id' => $contactId,
                'payment_id' => $payment->id,
                'error' => $e->getMessage(),
            ]);

            // Don't re-throw - LeadTeh integration failures shouldn't block payment processing
        }
    }



    /**
     * Handle a job failure.
     */
    public function failed(PaymentReceived $event, \Throwable $exception): void
    {
        $user = $event->order->user;
        $contactId = $user->additional_data['contact_id'] ?? 'unknown';

        Log::error("Failed to update LeadTeh variables after retries", [
            'user_id' => $user->id,
            'contact_id' => $contactId,
            'payment_id' => $event->payment->id,
            'error' => $exception->getMessage(),
        ]);
    }
}
