<?php

namespace App\Http\Controllers;

use App\Models\User;
use App\Models\Announce;
use App\Models\Setting;
use App\Services\HelperService;
use App\Services\SubscriptionService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use Carbon\Carbon;

class SubscriptionController extends Controller
{
    public function __construct(
        private SubscriptionService $subscriptionService,
        private HelperService $helperService,
    ) {}

    /**
     * Get subscription by UUID.
     */
    public function show(Request $request, string $uuid): Response
    {
        $startTime = microtime(true);

        try {
            // Validate UUID format early
            if (!$this->helperService->isValidUuid($uuid)) {
                Log::warning("Invalid UUID format: {$uuid}", [
                    'ip' => $request->ip(),
                    'user_agent' => $request->userAgent(),
                ]);
                return response('Invalid UUID format', 400);
            }

            // Check if user exists first (fast DB query)
            $user = User::findByUuid($uuid);
            if (!$user) {
                Log::info("User not found for UUID: {$uuid}", [
                    'ip' => $request->ip(),
                    'user_agent' => $request->userAgent(),
                ]);
                return response('User not found', 404);
            }
            // dd($user);

            // Use cache for frequently requested subscriptions (5 minutes TTL)
            // $cacheKey = "subscription:{$uuid}";
            // $vlessContent = Cache::remember($cacheKey, 300, function () use ($user) {
            //     return $this->generateVlessContent($user);
            // });
            // кэширование больше не нужно, должны отдавать всегда свежие данные

            // Get subscription using new service
            $subscription = $this->subscriptionService->getSubscriptionByUuid($uuid);
            $vlessContent = $subscription ? $subscription->content : '';

            // это не нужно, так как сервер x-ui сам блокирует подключение к vless
            // if (!$vlessContent) {
            //     Log::info("No active subscriptions found for UUID: {$uuid}", [
            //         'ip' => $request->ip(),
            //         'user_agent' => $request->userAgent(),
            //         'user_id' => $user->id,
            //     ]);
            //     return response('No active subscriptions found', 404);
            // }

            $responseTime = round((microtime(true) - $startTime) * 1000, 2);

            Log::info("Subscription delivered for UUID: {$uuid}", [
                'ip' => $request->ip(),
                'user_agent' => $request->userAgent(),
                'user_id' => $user->id,
                'content_length' => strlen($vlessContent),
                'response_time_ms' => $responseTime,
                // 'cached' => Cache::has($cacheKey),
            ]);

            // Check if request is from a browser
            if ($this->helperService->isBrowserUserAgent($request)) {
                // Return HTML view for browser requests
                return $this->renderSubscriptionView($user, $vlessContent, $uuid);
            }

            // Return vless content with appropriate headers for V2Ray clients
            return response($vlessContent, 200, $this->getSubscriptionHeaders($uuid));

        } catch (\Exception $e) {
            $responseTime = round((microtime(true) - $startTime) * 1000, 2);

            Log::error("Error delivering subscription for UUID: {$uuid}", [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'ip' => $request->ip(),
                'user_agent' => $request->userAgent(),
                'response_time_ms' => $responseTime,
            ]);

            return response('Internal server error', 500);
        }
    }

    /**
     * Show modern subscription page for a user.
     */
    public function showModern(Request $request, string $uuid): Response
    {
        try {
            if (!$this->helperService->isValidUuid($uuid)) {
                return response('Invalid UUID format', 400);
            }

            $user = User::findByUuid($uuid);
            if (!$user) {
                return response('User not found', 404);
            }

            // Get subscription using new service
            $subscription = $this->subscriptionService->getSubscriptionByUuid($uuid);
            $vlessContent = $subscription ? $subscription->content : '';

            // Check if request is from a browser
            if (! $this->helperService->isBrowserUserAgent($request)) {
                // Return vless content with appropriate headers for V2Ray clients
                return response($vlessContent, 200, $this->getSubscriptionHeaders($uuid));
            }


            // Always render modern view for this endpoint
            return $this->renderModernSubscriptionView($user, $vlessContent, $uuid);

        } catch (\Exception $e) {
            Log::error("Error getting modern subscription for UUID: {$uuid}", [
                'error' => $e->getMessage(),
                'ip' => $request->ip(),
            ]);

            return response('Internal server error', 500);
        }
    }

    /**
     * Show modern subscription page without QR code for a user.
     */
    public function showModernNoQR(Request $request, string $uuid): Response
    {
        try {
            if (!$this->helperService->isValidUuid($uuid)) {
                return response('Invalid UUID format', 400);
            }

            $user = User::findByUuid($uuid);
            if (!$user) {
                return response('User not found', 404);
            }

            // Get subscription using new service
            $subscription = $this->subscriptionService->getSubscriptionByUuid($uuid);
            $vlessContent = $subscription ? $subscription->content : '';

            // Render modern view without QR
            return $this->renderModernNoQRSubscriptionView($user, $vlessContent, $uuid);

        } catch (\Exception $e) {
            Log::error("Error getting modern no-QR subscription for UUID: {$uuid}", [
                'error' => $e->getMessage(),
                'ip' => $request->ip(),
            ]);

            return response('Internal server error', 500);
        }
    }

    /**
     * Get user statistics (for debugging/monitoring).
     */
    public function stats(Request $request, string $uuid): JsonResponse|Response
    {
        try {
            if (!$this->helperService->isValidUuid($uuid)) {
                return response('Invalid UUID format', 400);
            }

            // Get user and subscription data
            $user = User::findByUuid($uuid);
            if (!$user) {
                return response('User not found', 404);
            }

            $subscription = $this->subscriptionService->getSubscriptionByUuid($uuid);
            $stats = [
                'upload' => $subscription ? $subscription->upload : $user->up_traffic,
                'download' => $subscription ? $subscription->download : $user->down_traffic,
                'total' => $subscription ? $subscription->total : $user->total_gb,
                'expire' => $user->expiry_time,
            ];

            if (!$stats) {
                return response('User not found', 404);
            }

            return response()->json($stats);

        } catch (\Exception $e) {
            Log::error("Error getting stats for UUID: {$uuid}", [
                'error' => $e->getMessage(),
                'ip' => $request->ip(),
            ]);

            return response('Internal server error', 500);
        }
    }

    /**
     * Update user traffic statistics from XUI servers.
     */
    public function updateUserTraffic(Request $request, string $uuid)
    {
        try {
            if (!Str::isUuid($uuid)) {
                return response('Invalid UUID format', 400);
            }

            $user = User::findByUuid($uuid);
            if (!$user) {
                return response('User not found', 404);
            }

            // Update traffic statistics
            $this->subscriptionService->updateUserTrafficFromServers($user);

            return response()->json([
                'success' => true,
                'message' => 'Traffic statistics updated successfully',
                'data' => [
                    'upload' => $user->fresh()->up_traffic,
                    'download' => $user->fresh()->down_traffic,
                    'total' => $user->total_gb,
                ]
            ]);

        } catch (\Exception $e) {
            Log::error("Failed to update user traffic", [
                'uuid' => $uuid,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return response('Internal server error', 500);
        }
    }

    /**
     * Generate vless content from user's clients.
     * @deprecated This method is deprecated. Use SubscriptionService::getSubscriptionByUuid() instead.
     */
    private function generateVlessContent(User $user): string
    {
        // Get ALL clients for this user from all servers and inbounds (including expired)
        $clients = $user->xuiClients()
            ->with(['xuiInbound.xuiServer'])
            ->whereHas('xuiServer', function ($query) {
                $query->where('is_active', true);
            })
            ->whereHas('xuiInbound', function ($query) {
                $query->where('enable', true);
            })
            // ->where('enable', true)
            // ->whereNull('disabled_at')
            // Remove expired filter - return content for all clients
            ->get();

        // dd($clients);

        if ($clients->isEmpty()) {
            return '';
        }

        $vlessLinks = [];
        $totalUpload = 0;
        $totalDownload = 0;
        $totalLimit = 0;

        foreach ($clients as $client) {
            try {

                // Generate vless URL from stored inbound data
                $vlessUrl = $client->generateVlessUrl();

                if ($vlessUrl) {
                    $vlessLinks[] = $vlessUrl;

                    // Accumulate usage stats for headers
                    $totalUpload += $client->up_traffic;
                    $totalDownload += $client->down_traffic;
                    $totalLimit += $client->total_gb;
                }
            } catch (\Exception $e) {
                Log::error("Error generating vless URL for client {$client->client_id}", [
                    'server' => $client->xuiServer->name ?? 'unknown',
                    'inbound' => $client->xuiInbound->remark ?? 'unknown',
                    'error' => $e->getMessage(),
                ]);
            }
        }

        if (empty($vlessLinks)) {
            return '';
        }

        // Return vless URLs separated by newlines
        return implode("\n", $vlessLinks);
    }



    private function getSubscriptionTitle(?string $uuid = null): string
    {
        $title = config("app.subs_title", "no-title");

        if ($uuid) {
            $user = User::findByUuid($uuid);
            if ($user) {
                $userClientId = str_replace("client", "", $user->email);
                $title .= " \n       ID клиента: {$userClientId} 🔰";
            }
        }

        return $title;
    }

    private function getSubscriptionAnnounce(): string
    {
        return config("app.subs_announce", "");
    }

    /**
     * Get announce data based on subscription expiry status.
     */
    private function getAnnounceData(?string $uuid = null): array
    {
        // Default fallback
        $defaultText = $this->getSubscriptionAnnounce();
        $defaultUrl = config("app.subs_announce_url", "");

        if (!$uuid) {
            return [
                'text' => $defaultText,
                'url' => $defaultUrl,
            ];
        }

        try {
            $user = User::findByUuid($uuid);
            if (!$user) {
                return [
                    'text' => $defaultText,
                    'url' => $defaultUrl,
                ];
            }

            $defaultUrl = $this->helperService->getCompiledTextVariables($defaultUrl, $user);

            // Get earliest expiry time from user's current subscription or user model
            $currentSubscription = $user->getCurrentSubscription();
            $earliestExpiry = $currentSubscription
                ? $currentSubscription->expires_at
                : $user->expiry_time;

            if ($earliestExpiry) {
                $now = Carbon::now();
                $expiryTime = Carbon::parse($earliestExpiry);

                // Check if subscription has already expired
                if ($expiryTime->isPast()) {

                    if ($user->demo_until) {
                        return [
                            'text' => "❗️ #c11e14ДЕМО-ВЕРСИЯ ЗАКОНЧИЛАСЬ ❗️\n\nНажмите сюда, чтобы продлить подписку 💳",
                            'url' => config('app.url') . "/subs/{$uuid}/renew",
                        ];
                    }

                    return [
                        'text' => "❗️ #c11e14ПОДПИСКА #c11e14ИСТЕКЛА ❗️\n\nНажмите сюда, чтобы продлить подписку 💳",
                        'url' => config('app.url') . "/subs/{$uuid}/renew",
                    ];
                }

                // Check if expiry is within 3 days
                if ($expiryTime->isFuture() && $now->diffInDays($expiryTime, false) < 3) {
                    // Calculate remaining time
                    $remainingTime = $this->formatRemainingTime($now, $expiryTime);

                    if ($user->demo_until) {
                        return [
                            'text' => "🪫 #c11e14ДЕМО-ВЕРСИЯ ЗАКОНЧИТСЯ через {$remainingTime}\n\n#c11e14Нажмите сюда, чтобы продлить подписку 💳",
                            'url' => config('app.url') . "/subs/{$uuid}/renew",
                        ];
                    }

                    return [
                        'text' => "🪫 ПОДПИСКА #c11e14ЗАКОНЧИТСЯ через {$remainingTime}\n\n#c11e14Нажмите сюда, чтобы продлить подписку 💳",
                        'url' => config('app.url') . "/subs/{$uuid}/renew",
                    ];
                }
            }

            // If not expiring soon, get latest announce from database
            $latestAnnounce = Announce::getLatest();

            if ($latestAnnounce) {

                $url = $this->helperService->getCompiledTextVariables($latestAnnounce->url, $user);

                return [
                    'text' => $latestAnnounce->message,
                    'url' => $url ?: $defaultUrl,
                ];
            }

            // Fallback to default
            return [
                'text' => $defaultText,
                'url' => $defaultUrl,
            ];

        } catch (\Exception $e) {
            Log::error("Error getting announce data for UUID: {$uuid}", [
                'error' => $e->getMessage(),
            ]);

            return [
                'text' => $defaultText,
                'url' => $defaultUrl,
            ];
        }
    }

    /**
     * Format remaining time in a human-readable format.
     */
    private function formatRemainingTime(Carbon $now, Carbon $expiry): string
    {
        $diff = $now->diff($expiry);

        if ($diff->d > 0) {
            return $diff->d . ' ' . $this->pluralize($diff->d, 'день', 'дня', 'дней');
        } elseif ($diff->h > 0) {
            return $diff->h . ' ' . $this->pluralize($diff->h, 'час', 'часа', 'часов')
                . ' ' . $diff->i . ' ' . $this->pluralize($diff->i, 'минута', 'минуты', 'минут');
        } else {
            return $diff->i . ' ' . $this->pluralize($diff->i, 'минута', 'минуты', 'минут');
        }
    }

    /**
     * Russian pluralization helper.
     */
    private function pluralize(int $number, string $one, string $few, string $many): string
    {
        $mod10 = $number % 10;
        $mod100 = $number % 100;

        if ($mod100 >= 11 && $mod100 <= 19) {
            return $many;
        }

        if ($mod10 == 1) {
            return $one;
        }

        if ($mod10 >= 2 && $mod10 <= 4) {
            return $few;
        }

        return $many;
    }

    private function getRoutingRules(): string
    {
        return <<<JSON
{"domainStrategy":"AsIs","id":"1EAA88BB-B5F5-4F69-82D0-9FF449908794","balancers":[],"domainMatcher":"hybrid","rules":[{"domain":["regexp:.*\\\\.ru$","geosite:category-ru"],"id":"9CA62C69-3D7A-4FE5-9E19-2189E2E4853E","outboundTag":"direct","type":"field","__name__":"Direct Russia","ip":["geoip:ru"]}],"name":"Example"}
JSON;
    }

    private function getRoutingRulesEncoded(): string
    {
        return "eyJkb21haW5TdHJhdGVneSI6IklQT25EZW1hbmQiLCJpZCI6IjA4NTFEREQxLTMzQjMtNDI0OC1CQ0JGLUY3RkZBMkU3MjRGQSIsImJhbGFuY2VycyI6W10sImRvbWFpbk1hdGNoZXIiOiJoeWJyaWQiLCJuYW1lIjoiUnVsZXNldCIsInJ1bGVzIjpbeyJuYW1lIjoi0KLQvtGA0YDQtdC90YIg0L3QsNC/0YDRj9C80YPRjiIsImlkIjoiMTA2NDE4NjEtRTE1Ri00NDk3LThERDEtMzZGNTcwMjUzRjhEIiwidHlwZSI6ImZpZWxkIiwicHJvdG9jb2wiOlsiYml0dG9ycmVudCJdLCJvdXRib3VuZFRhZyI6ImRpcmVjdCJ9LHsibmFtZSI6ItCR0LvQvtC60LjRgNC+0LLQutCwINGA0LXQutC70LDQvNGLIiwiaWQiOiJCQTBGMEUwQi1EQjFBLTQ0RTktQTVCMS05MzBCRUMwNkFGQjAiLCJ0eXBlIjoiZmllbGQiLCJkb21haW4iOlsiZ2Vvc2l0ZTpjYXRlZ29yeS1hZHMtYWxsIl0sIm91dGJvdW5kVGFnIjoiYmxvY2sifSx7Im5hbWUiOiLQn9GA0LjQstCw0YLQvdGL0LUg0YHQtdGC0Lgg0L3QsNC/0YDRj9C80YPRjiIsImlkIjoiRTQ2RjJFREUtNjg3QS00QzVFLTgxQ0ItOTE5NDVCN0QzRjg3IiwidHlwZSI6ImZpZWxkIiwiaXAiOlsiZ2VvaXA6cHJpdmF0ZSJdLCJvdXRib3VuZFRhZyI6ImRpcmVjdCJ9LHsibmFtZSI6ItCf0YDQuNCy0LDRgtC90YvQtSDQtNC+0LzQtdC90Ysg0L3QsNC/0YDRj9C80YPRjiIsImlkIjoiRTNGMEU4NjktQjEzRC00RkMzLTgwQkItNkMzQjk1NkY2QTQyIiwidHlwZSI6ImZpZWxkIiwiZG9tYWluIjpbImdlb3NpdGU6cHJpdmF0ZSJdLCJvdXRib3VuZFRhZyI6ImRpcmVjdCJ9LHsibmFtZSI6ItCU0L7RgdGC0YPQv9C90YvQtSDRgtC+0LvRjNC60L4g0LIg0KDQvtGB0YHQuNC4INC90LDQv9GA0Y/QvNGD0Y4iLCJpZCI6IjlCNDRGQjZGLTY4NjMtNENBNy1CMDg4LTJENjkyODIxQUNGNCIsInR5cGUiOiJmaWVsZCIsImlwIjpbImdlb2lwOnJ1Il0sIm91dGJvdW5kVGFnIjoiZGlyZWN0In0seyJuYW1lIjoiMmlwLnJ1INC90LDQv9GA0Y/QvNGD0Y4iLCJpZCI6IjBGRTQyQUVFLTFDMUEtNDhDMy05QkI4LTZBQUYxN0U3QUY5RCIsInR5cGUiOiJmaWVsZCIsImRvbWFpbiI6WyJkb21haW46MmlwLnJ1Il0sIm91dGJvdW5kVGFnIjoiZGlyZWN0In0seyJuYW1lIjoi0J7RgdGC0LDQu9GM0L3QvtC1INCyINC/0YDQvtC60YHQuCIsImlkIjoiODIwMkNDMjgtQTA2Qy00MjFDLTgxQzgtQTY3QzQwMjEzNEFBIiwidHlwZSI6ImZpZWxkIiwicG9ydCI6IjAtNjU1MzUiLCJvdXRib3VuZFRhZyI6InByb3h5In1dfQ==";
    }

    /**
     * Get subscription headers.
     */
    private function getSubscriptionHeaders(?string $uuid = null): array
    {
        // Get announce text and URL based on subscription expiry
        $announceData = $this->getAnnounceData($uuid);

        // Determine routing rules based on user preference
        $routingRules = '';
        if ($uuid) {
            $user = User::findByUuid($uuid);
            if ($user && $user->use_common_routing) {
                $routingRules = Setting::get('common_routing_rules', '', true);
                if (! empty($routingRules)) {
                    $routingRules = base64_encode($this->helperService->encodeJsonUtf8Safe($routingRules));
                }
            }
        }

        $headers = [
            'Profile-Title' => 'base64:' . base64_encode($this->getSubscriptionTitle($uuid)),
            'Content-Type' => 'text/plain; charset=utf-8',
            'Profile-Update-Interval' => '1', // update interval in hours
            'Profile-Web-Page-Url' => config('app.url') . '/' . $uuid,
            'Support-Url' => config('app.url') . '/support/' . $uuid,
            'Announce' => 'base64:' . base64_encode($announceData['text']),
            'Announce-Url' => $announceData['url'],
            'Update-always' => 'true',
            'Routing' => $routingRules,
        ];

        // Generate real-time usage stats
        if ($uuid) {
            // Reuse user if already loaded, otherwise load it
            if (!isset($user)) {
                $user = User::findByUuid($uuid);
            }
            if ($user) {
                // Get subscription data from new service
                $subscription = $this->subscriptionService->getSubscriptionByUuid($uuid);

                if ($subscription) {
                    $totalUpload = $subscription->upload;
                    $totalDownload = $subscription->download;
                    $totalLimit = $subscription->total;
                } else {
                    // Fallback to user data
                    $totalUpload = $user->up_traffic;
                    $totalDownload = $user->down_traffic;
                    $totalLimit = $user->total_gb;
                }

                // If there is no traffic consumption at all, then we will specify at least 1 byte so that the traffic scale (traffic progress bar) starts to be displayed in the v2raytun applications
                if ($totalUpload === 0 && $totalDownload === 0) {
                    $totalDownload = 1;
                }

                // Get expiry time from User model (centralized source)
                $earliestExpiry = $user->expiry_time;

                // Build userinfo string
                $userinfo = sprintf('upload=%d; download=%d', $totalUpload, $totalDownload);

                // Add total only if there are actual limits set
                if ($totalLimit > 0) {
                    $userinfo .= sprintf('; total=%d', $totalLimit);
                }

                // Always add expire if there's an expiry time (even if expired)
                if ($earliestExpiry) {
                    $expireTimestamp = $earliestExpiry->timestamp;
                    $userinfo .= sprintf('; expire=%d', $expireTimestamp);
                }

                $headers['Subscription-Userinfo'] = $userinfo;
            }
        }

        return $headers;
    }

    /**
     * Update user's common routing preference.
     */
    public function updateRoutingPreference(Request $request, string $uuid): JsonResponse
    {
        try {
            if (!$this->helperService->isValidUuid($uuid)) {
                return response()->json([
                    'success' => false,
                    'message' => 'Invalid UUID format',
                ], 400);
            }

            $user = User::findByUuid($uuid);
            if (!$user) {
                return response()->json([
                    'success' => false,
                    'message' => 'User not found',
                ], 404);
            }

            $request->validate([
                'use_common_routing' => 'required|boolean',
            ]);

            $user->update([
                'use_common_routing' => $request->use_common_routing,
            ]);

            Log::info('User routing preference updated', [
                'user_id' => $user->id,
                'uuid' => $uuid,
                'tg_id' => $user->tg_id,
                'use_common_routing' => $request->use_common_routing,
                'ip' => $request->ip(),
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Настройка обновлена',
                'data' => [
                    'use_common_routing' => $user->use_common_routing,
                ],
            ]);

        } catch (\Illuminate\Validation\ValidationException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Некорректные данные',
                'errors' => $e->errors(),
            ], 422);
        } catch (\Exception $e) {
            Log::error('Failed to update routing preference', [
                'uuid' => $uuid,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Ошибка при обновлении настройки',
            ], 500);
        }
    }


    /**
     * Render subscription view for browser requests.
     */
    private function renderSubscriptionView(User $user, string $vlessContent, string $uuid): Response
    {
        // Parse vless links from content
        $vlessLinks = array_filter(explode("\n", $vlessContent));

        // Get current subscription data
        $currentSubscription = $user->getCurrentSubscription();

        // Get statistics from User model (centralized source) and subscription
        $totalUpload = $user->up_traffic;
        $totalDownload = $user->down_traffic;
        $totalLimit = $user->total_gb;
        $earliestExpiry = $user->expiry_time;

        // If we have a current subscription, use its data for more accurate info
        if ($currentSubscription) {
            $totalLimit = $currentSubscription->traffic_limit_gb
                ? $currentSubscription->traffic_limit_gb * 1024 * 1024 * 1024 // Convert GB to bytes
                : 0; // 0 means unlimited
            $earliestExpiry = $currentSubscription->expires_at;
        }

        // Determine status
        $isExpired = $earliestExpiry && $earliestExpiry->isPast();
        $isLimited = $totalLimit > 0 && ($totalUpload + $totalDownload) >= $totalLimit;
        $status = $isLimited ? 'limited' : ($isExpired ? 'expired' : 'active');

        // Calculate remaining time with appropriate units
        $remainingTime = $this->calculateRemainingTime($earliestExpiry);

        // Prepare data for view
        $data = [
            'email' => $user->email ?? 'User',
            'subscription_url' => config('app.url') . '/subs/' . $user->uuid,
            'vless_links' => $vlessLinks,
            'up' => $totalUpload,
            'down' => $totalDownload,
            'total' => $totalLimit ?: 0,
            'used_traffic' => $totalUpload + $totalDownload,
            'data_limit' => $totalLimit ?: 0,
            'expiry_time' => $earliestExpiry,
            'remaining_time' => $remainingTime,
            'reset_interval' => 'none',
            'enable' => !$isExpired && !$isLimited,
            'status' => $status,
            'uuid' => $uuid,
            'subscription' => $currentSubscription ? [
                'plan_name' => $currentSubscription->subscriptionPlan->name,
                'is_demo' => $currentSubscription->subscriptionPlan->is_demo,
                'is_active' => $currentSubscription->is_active,
                'is_expired' => $currentSubscription->isExpired(),
                'started_at' => $currentSubscription->started_at,
                'expires_at' => $currentSubscription->expires_at,
                'traffic_limit_gb' => $currentSubscription->traffic_limit_gb,
                'traffic_used_gb' => $currentSubscription->traffic_used_gb,
                'renewal_required' => $currentSubscription->isRenewalRequired(),
            ] : null,
        ];

        return response()->view('subs.subscription', compact('data'));
    }

    /**
     * Render modern subscription view for browser requests.
     */
    private function renderModernSubscriptionView(User $user, string $vlessContent, string $uuid): Response
    {
        // Parse vless links from content
        $vlessLinks = array_filter(explode("\n", $vlessContent));

        // Get current subscription data
        $currentSubscription = $user->getCurrentSubscription();

        // Get statistics from User model (centralized source) and subscription
        $totalUpload = $user->up_traffic;
        $totalDownload = $user->down_traffic;
        $totalLimit = $user->total_gb;
        $earliestExpiry = $user->expiry_time;

        // If we have a current subscription, use its data for more accurate info
        if ($currentSubscription) {
            $totalLimit = $currentSubscription->traffic_limit_gb
                ? $currentSubscription->traffic_limit_gb * 1024 * 1024 * 1024 // Convert GB to bytes
                : 0; // 0 means unlimited
            $earliestExpiry = $currentSubscription->expires_at;
        }

        // Determine status
        $isExpired = $earliestExpiry && $earliestExpiry->isPast();
        $isLimited = $totalLimit > 0 && ($totalUpload + $totalDownload) >= $totalLimit;
        $status = $isLimited ? 'limited' : ($isExpired ? 'expired' : 'active');

        // Calculate remaining time with appropriate units
        $remainingTime = $this->calculateRemainingTime($earliestExpiry);

        // Prepare data for modern view
        $data = [
            'client_id' => str_replace("client", "", $user->email),
            'email' => $user->email ?? 'User',
            'subscription_url' => config('app.url') . '/subs/' . $user->uuid,
            'vless_links' => $vlessLinks,
            'vless_content' => $vlessContent,
            'up' => $totalUpload,
            'down' => $totalDownload,
            'total' => $totalLimit ?: 0,
            'used_traffic' => $totalUpload + $totalDownload,
            'data_limit' => $totalLimit ?: 0,
            'expiry_time' => $earliestExpiry,
            'remaining_time' => $remainingTime,
            'reset_interval' => 'none',
            'enable' => !$isExpired && !$isLimited,
            'status' => $status,
            'uuid' => $uuid,
            'use_common_routing' => $user->use_common_routing,
            'subscription' => $currentSubscription ? [
                'plan_name' => $currentSubscription->subscriptionPlan->name,
                'is_demo' => $currentSubscription->subscriptionPlan->is_demo,
                'is_active' => $currentSubscription->is_active,
                'is_expired' => $currentSubscription->isExpired(),
                'started_at' => $currentSubscription->started_at,
                'expires_at' => $currentSubscription->expires_at,
                'traffic_limit_gb' => $currentSubscription->traffic_limit_gb,
                'traffic_used_gb' => $currentSubscription->traffic_used_gb,
                'remaining_days' => $currentSubscription->getRemainingDays(),
                'traffic_usage_percentage' => $currentSubscription->getTrafficUsagePercentage(),
                'renewal_required' => $currentSubscription->isRenewalRequired(),
            ] : null,
        ];

        return response()->view('subs.subscription-modern-simple', compact('data'));
    }

    /**
     * Render modern subscription view without QR for browser requests.
     */
    private function renderModernNoQRSubscriptionView(User $user, string $vlessContent, string $uuid): Response
    {
        // Parse vless links from content
        $vlessLinks = array_filter(explode("\n", $vlessContent));

        // Get current subscription data
        $currentSubscription = $user->getCurrentSubscription();

        // Get statistics from User model (centralized source) and subscription
        $totalUpload = $user->up_traffic;
        $totalDownload = $user->down_traffic;
        $totalLimit = $user->total_gb;
        $earliestExpiry = $user->expiry_time;

        // If we have a current subscription, use its data for more accurate info
        if ($currentSubscription) {
            $totalLimit = $currentSubscription->traffic_limit_gb
                ? $currentSubscription->traffic_limit_gb * 1024 * 1024 * 1024 // Convert GB to bytes
                : 0; // 0 means unlimited
            $earliestExpiry = $currentSubscription->expires_at;
        }

        // Determine status
        $isExpired = $earliestExpiry && $earliestExpiry->isPast();
        $isLimited = $totalLimit > 0 && ($totalUpload + $totalDownload) >= $totalLimit;
        $status = $isLimited ? 'limited' : ($isExpired ? 'expired' : 'active');

        // Calculate remaining time with appropriate units
        $remainingTime = $this->calculateRemainingTime($earliestExpiry);

        // Prepare data for modern view without QR
        $data = [
            'email' => $user->email ?? 'User',
            'subscription_url' => config('app.url') . '/subs/' . $user->uuid,
            'vless_links' => $vlessLinks,
            'vless_content' => $vlessContent,
            'up' => $totalUpload,
            'down' => $totalDownload,
            'total' => $totalLimit ?: 0,
            'used_traffic' => $totalUpload + $totalDownload,
            'data_limit' => $totalLimit ?: 0,
            'expiry_time' => $earliestExpiry,
            'remaining_time' => $remainingTime,
            'reset_interval' => 'none',
            'enable' => !$isExpired && !$isLimited,
            'status' => $status,
            'uuid' => $uuid,
            'subscription' => $currentSubscription ? [
                'plan_name' => $currentSubscription->subscriptionPlan->name,
                'is_active' => $currentSubscription->is_active,
                'is_expired' => $currentSubscription->isExpired(),
                'started_at' => $currentSubscription->started_at,
                'expires_at' => $currentSubscription->expires_at,
                'traffic_limit_gb' => $currentSubscription->traffic_limit_gb,
                'traffic_used_gb' => $currentSubscription->traffic_used_gb,
            ] : null,
        ];

        return response()->view('subs.subscription-modern-no-qr', compact('data'));
    }

    /**
     * Force refresh subscription content for a user.
     */
    public function refresh(Request $request, string $uuid): JsonResponse|Response
    {
        try {
            if (!$this->helperService->isValidUuid($uuid)) {
                return response('Invalid UUID format', 400);
            }

            $user = User::findByUuid($uuid);
            if (!$user) {
                return response('User not found', 404);
            }

            // Clear cache for this user
            Cache::forget("subscription:{$uuid}");

            // Update traffic statistics for the user
            $refreshedCount = 0;
            try {
                $this->subscriptionService->updateUserTrafficFromServers($user);
                $refreshedCount = 1;
            } catch (\Exception $e) {
                Log::warning("Failed to update traffic for user {$user->uuid}", [
                    'user_id' => $user->id,
                    'error' => $e->getMessage(),
                ]);
                $refreshedCount = 0;
            }

            // Get server pool info for response
            $currentSubscription = $user->getCurrentSubscription();
            $serverPool = $currentSubscription?->serverPool ?? $user->serverPool;
            $totalServers = $serverPool ? $serverPool->servers()->where('is_active', true)->count() : 0;

            Log::info("Forced refresh for UUID: {$uuid}", [
                'ip' => $request->ip(),
                'user_id' => $user->id,
                'traffic_updated' => $refreshedCount > 0,
                'subscription_id' => $currentSubscription?->id,
                'server_pool_id' => $serverPool?->id,
            ]);

            return response()->json([
                'message' => 'Subscription content refreshed',
                'traffic_updated' => $refreshedCount > 0,
                'total_servers' => $totalServers,
                'subscription' => $currentSubscription ? [
                    'plan_name' => $currentSubscription->subscriptionPlan->name,
                    'is_active' => $currentSubscription->is_active,
                    'expires_at' => $currentSubscription->expires_at,
                ] : null,
            ]);

        } catch (\Exception $e) {
            Log::error("Error refreshing subscription for UUID: {$uuid}", [
                'error' => $e->getMessage(),
                'ip' => $request->ip(),
            ]);

            return response('Internal server error', 500);
        }
    }


    /**
     * Calculate remaining time with appropriate units.
     */
    private function calculateRemainingTime($expiryTime): array
    {
        if (!$expiryTime || $expiryTime->isPast()) {
            return [
                'value' => 0,
                'unit' => 'expired',
                'display' => 'Истекла',
                'display_en' => 'Expired'
            ];
        }

        $now = now();
        $diff = $now->diff($expiryTime);

        $totalHours = $diff->days * 24 + $diff->h;

        // If less than 1 hour, show minutes
        if ($totalHours < 1) {
            $minutes = $diff->i;
            return [
                'value' => $minutes,
                'unit' => 'minutes',
                'display' => $minutes . ' мин.',
                'display_en' => $minutes . ' min.'
            ];
        }

        // If less than 24 hours, show hours and minutes
        if ($diff->days < 1) {
            $hours = $totalHours;
            $minutes = $diff->i;

            if ($minutes > 0) {
                return [
                    'value' => $hours,
                    'unit' => 'hours',
                    'display' => $hours . ' ч. ' . $minutes . ' мин.',
                    'display_en' => $hours . ' h. ' . $minutes . ' min.'
                ];
            } else {
                return [
                    'value' => $hours,
                    'unit' => 'hours',
                    'display' => $hours . ' ч.',
                    'display_en' => $hours . ' h.'
                ];
            }
        }

        // Otherwise show days
        return [
            'value' => $diff->days,
            'unit' => 'days',
            'display' => $diff->days . ' дн.',
            'display_en' => $diff->days . ' days'
        ];
    }
}
