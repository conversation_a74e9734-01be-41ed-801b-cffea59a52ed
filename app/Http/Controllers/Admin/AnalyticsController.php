<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\Order;
use App\Models\Payment;
use App\Models\SystemLog;
use App\Models\BackupHistory;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class AnalyticsController extends Controller
{
    /**
     * Get dashboard overview statistics.
     */
    public function dashboard(): JsonResponse
    {
        try {
            $now = now();
            $startOfMonth = $now->startOfMonth()->copy();
            $startOfWeek = $now->startOfWeek()->copy();

            // Users statistics
            $totalUsers = User::count();
            $activeUsers = User::whereHas('currentSubscription', function ($q) {
                $q->where('expires_at', '>', now())
                  ->where('is_active', true);
            })->count();
            $newUsersThisMonth = User::where('created_at', '>=', $startOfMonth)->count();
            $newUsersThisWeek = User::where('created_at', '>=', $startOfWeek)->count();

            // Revenue statistics
            $totalRevenue = Payment::where('status', 'completed')
                ->sum(DB::raw('amount_in_cents / 100'));
            $revenueThisMonth = Payment::where('status', 'completed')
                ->where('created_at', '>=', $startOfMonth)
                ->sum(DB::raw('amount_in_cents / 100'));
            $revenueThisWeek = Payment::where('status', 'completed')
                ->where('created_at', '>=', $startOfWeek)
                ->sum(DB::raw('amount_in_cents / 100'));

            // Orders statistics
            $totalOrders = Order::count();
            $paidOrders = Order::where('status', 'paid')->count();
            $pendingOrders = Order::where('status', 'pending')->count();

            // System health
            $recentErrors = SystemLog::errors()->recent(24)->count();
            $recentBackups = BackupHistory::successful()
                ->where('created_at', '>=', $now->subDays(7))
                ->count();

            return response()->json([
                'success' => true,
                'data' => [
                    'users' => [
                        'total' => $totalUsers,
                        'active' => $activeUsers,
                        'new_this_month' => $newUsersThisMonth,
                        'new_this_week' => $newUsersThisWeek,
                        'active_percentage' => $totalUsers > 0 ? round(($activeUsers / $totalUsers) * 100, 1) : 0,
                    ],
                    'revenue' => [
                        'total' => $totalRevenue,
                        'this_month' => $revenueThisMonth,
                        'this_week' => $revenueThisWeek,
                        'currency' => 'RUB',
                    ],
                    'orders' => [
                        'total' => $totalOrders,
                        'paid' => $paidOrders,
                        'pending' => $pendingOrders,
                        'conversion_rate' => $totalOrders > 0 ? round(($paidOrders / $totalOrders) * 100, 1) : 0,
                    ],
                    'system' => [
                        'recent_errors' => $recentErrors,
                        'recent_backups' => $recentBackups,
                        'health_status' => $recentErrors < 10 ? 'good' : ($recentErrors < 50 ? 'warning' : 'critical'),
                    ],
                ],
            ]);

        } catch (\Exception $e) {
            Log::error("Exception getting dashboard analytics", [
                'error' => $e->getMessage(),
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Internal server error',
            ], 500);
        }
    }

    /**
     * Get system logs.
     */
    public function systemLogs(Request $request): JsonResponse
    {
        try {
            $request->validate([
                'level' => 'nullable|string|in:info,warning,error,debug',
                'category' => 'nullable|string|in:payment,subscription,xui,leadteh,backup',
                'hours' => 'nullable|integer|min:1|max:168', // max 1 week
                'page' => 'integer|min:1',
                'per_page' => 'integer|min:1|max:100',
            ]);

            $query = SystemLog::with(['user', 'server']);

            if ($request->level) {
                $query->where('level', $request->level);
            }

            if ($request->category) {
                $query->where('category', $request->category);
            }

            $hours = $request->get('hours', 24);
            $query->where('created_at', '>=', now()->subHours($hours));

            $perPage = $request->get('per_page', 50);
            $logs = $query->orderBy('created_at', 'desc')->paginate($perPage);

            return response()->json([
                'success' => true,
                'data' => $logs->items(),
                'pagination' => [
                    'current_page' => $logs->currentPage(),
                    'last_page' => $logs->lastPage(),
                    'per_page' => $logs->perPage(),
                    'total' => $logs->total(),
                ],
            ]);

        } catch (\Illuminate\Validation\ValidationException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $e->errors(),
            ], 422);

        } catch (\Exception $e) {
            Log::error("Exception getting system logs", [
                'error' => $e->getMessage(),
                'request' => $request->all(),
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Internal server error',
            ], 500);
        }
    }

    /**
     * Get backup history.
     */
    public function backupHistory(Request $request): JsonResponse
    {
        try {
            $request->validate([
                'type' => 'nullable|string|in:server,full,restore',
                'status' => 'nullable|string|in:success,failed,partial',
                'days' => 'nullable|integer|min:1|max:90',
                'page' => 'integer|min:1',
                'per_page' => 'integer|min:1|max:100',
            ]);

            $query = BackupHistory::with(['server']);

            if ($request->type) {
                $query->where('type', $request->type);
            }

            if ($request->status) {
                $query->where('status', $request->status);
            }

            $days = $request->get('days', 30);
            $query->where('created_at', '>=', now()->subDays($days));

            $perPage = $request->get('per_page', 20);
            $backups = $query->orderBy('created_at', 'desc')->paginate($perPage);

            // Add computed fields
            $backups->getCollection()->transform(function ($backup) {
                return [
                    'id' => $backup->id,
                    'type' => $backup->type,
                    'filename' => $backup->filename,
                    'status' => $backup->status,
                    'server' => $backup->server ? [
                        'id' => $backup->server->id,
                        'name' => $backup->server->name,
                    ] : null,
                    'items_processed' => $backup->items_processed,
                    'items_failed' => $backup->items_failed,
                    'file_size' => $backup->formatted_file_size,
                    'duration' => $backup->duration,
                    'started_at' => $backup->started_at,
                    'completed_at' => $backup->completed_at,
                    'details' => $backup->details,
                ];
            });

            return response()->json([
                'success' => true,
                'data' => $backups->items(),
                'pagination' => [
                    'current_page' => $backups->currentPage(),
                    'last_page' => $backups->lastPage(),
                    'per_page' => $backups->perPage(),
                    'total' => $backups->total(),
                ],
            ]);

        } catch (\Illuminate\Validation\ValidationException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $e->errors(),
            ], 422);

        } catch (\Exception $e) {
            Log::error("Exception getting backup history", [
                'error' => $e->getMessage(),
                'request' => $request->all(),
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Internal server error',
            ], 500);
        }
    }

    /**
     * Get revenue analytics with charts data.
     */
    public function revenueAnalytics(Request $request): JsonResponse
    {
        try {
            $request->validate([
                'period' => 'string|in:week,month,quarter,year',
                'start_date' => 'date',
                'end_date' => 'date|after_or_equal:start_date',
            ]);

            $period = $request->get('period', 'month');
            $startDate = $request->start_date ? \Carbon\Carbon::parse($request->start_date) : null;
            $endDate = $request->end_date ? \Carbon\Carbon::parse($request->end_date) : null;

            // Set default dates based on period
            if (!$startDate || !$endDate) {
                switch ($period) {
                    case 'week':
                        $startDate = now()->startOfWeek();
                        $endDate = now()->endOfWeek();
                        break;
                    case 'quarter':
                        $startDate = now()->startOfQuarter();
                        $endDate = now()->endOfQuarter();
                        break;
                    case 'year':
                        $startDate = now()->startOfYear();
                        $endDate = now()->endOfYear();
                        break;
                    default: // month
                        $startDate = now()->startOfMonth();
                        $endDate = now()->endOfMonth();
                        break;
                }
            }

            // Get daily revenue data
            $dailyRevenue = Payment::where('status', 'completed')
                ->whereBetween('created_at', [$startDate, $endDate])
                ->selectRaw('DATE(created_at) as date, SUM(amount_in_cents) / 100 as revenue, COUNT(*) as transactions')
                ->groupBy('date')
                ->orderBy('date')
                ->get();

            // Get revenue by source
            $revenueBySource = Payment::where('payments.status', 'completed')
                ->whereBetween('payments.created_at', [$startDate, $endDate])
                ->join('orders', 'payments.order_id', '=', 'orders.id')
                ->join('users', 'orders.user_id', '=', 'users.id')
                ->selectRaw('users.source, SUM(payments.amount_in_cents) / 100 as revenue, COUNT(*) as count')
                ->groupBy('users.source')
                ->get();

            // Get revenue by subscription plans
            $revenueByPlan = Payment::where('payments.status', 'completed')
                ->whereBetween('payments.created_at', [$startDate, $endDate])
                ->join('orders', 'payments.order_id', '=', 'orders.id')
                ->join('subscription_plans', 'orders.subscription_plan_id', '=', 'subscription_plans.id')
                ->selectRaw('subscription_plans.name, subscription_plans.price_in_cents / 100 as plan_price, SUM(payments.amount_in_cents) / 100 as revenue, COUNT(*) as count')
                ->groupBy('subscription_plans.id', 'subscription_plans.name', 'subscription_plans.price_in_cents')
                ->orderByDesc('revenue')
                ->get();

            // Get hourly revenue distribution
            $hourlyRevenue = Payment::where('payments.status', 'completed')
                ->whereBetween('payments.created_at', [$startDate, $endDate])
                ->selectRaw('HOUR(payments.created_at) as hour, SUM(payments.amount_in_cents) / 100 as revenue, COUNT(*) as transactions')
                ->groupBy('hour')
                ->orderBy('hour')
                ->get();

            // Calculate growth metrics
            $previousPeriodStart = $startDate->copy()->sub($startDate->diffInDays($endDate) + 1, 'days');
            $previousPeriodEnd = $startDate->copy()->subDay();

            $currentRevenue = $dailyRevenue->sum('revenue');
            $previousRevenue = Payment::where('payments.status', 'completed')
                ->whereBetween('payments.created_at', [$previousPeriodStart, $previousPeriodEnd])
                ->sum(DB::raw('payments.amount_in_cents / 100'));

            $growthRate = $previousRevenue > 0 ? (($currentRevenue - $previousRevenue) / $previousRevenue) * 100 : 0;

            return response()->json([
                'success' => true,
                'data' => [
                    'period' => [
                        'start_date' => $startDate->toDateString(),
                        'end_date' => $endDate->toDateString(),
                        'type' => $period,
                    ],
                    'summary' => [
                        'total_revenue' => $currentRevenue,
                        'previous_revenue' => $previousRevenue,
                        'growth_rate' => round($growthRate, 2),
                        'total_transactions' => $dailyRevenue->sum('transactions'),
                        'average_transaction' => $dailyRevenue->sum('transactions') > 0 ?
                            round($currentRevenue / $dailyRevenue->sum('transactions'), 2) : 0,
                    ],
                    'charts' => [
                        'daily_revenue' => $dailyRevenue,
                        'revenue_by_source' => $revenueBySource,
                        'revenue_by_plan' => $revenueByPlan,
                        'hourly_distribution' => $hourlyRevenue,
                    ],
                ],
            ]);

        } catch (\Illuminate\Validation\ValidationException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $e->errors(),
            ], 422);

        } catch (\Exception $e) {
            Log::error("Exception getting revenue analytics", [
                'error' => $e->getMessage(),
                'request' => $request->all(),
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Internal server error',
            ], 500);
        }
    }

    /**
     * Get users analytics with charts data.
     */
    public function usersAnalytics(Request $request): JsonResponse
    {
        try {
            $request->validate([
                'period' => 'string|in:week,month,quarter,year',
            ]);

            $period = $request->get('period', 'month');

            // Set date range
            switch ($period) {
                case 'week':
                    $startDate = now()->startOfWeek();
                    $endDate = now()->endOfWeek();
                    $groupBy = 'DATE(created_at)';
                    break;
                case 'quarter':
                    $startDate = now()->startOfQuarter();
                    $endDate = now()->endOfQuarter();
                    $groupBy = 'DATE(created_at)';
                    break;
                case 'year':
                    $startDate = now()->startOfYear();
                    $endDate = now()->endOfYear();
                    $groupBy = 'YEAR(created_at), MONTH(created_at)';
                    break;
                default: // month
                    $startDate = now()->startOfMonth();
                    $endDate = now()->endOfMonth();
                    $groupBy = 'DATE(created_at)';
                    break;
            }

            // Get daily user registrations
            $dailyRegistrations = User::whereBetween('created_at', [$startDate, $endDate])
                ->selectRaw("$groupBy as period, COUNT(*) as count")
                ->groupByRaw($groupBy)
                ->orderBy('period')
                ->get();

            // Get users by source with percentages
            $usersBySource = User::whereBetween('created_at', [$startDate, $endDate])
                ->selectRaw('source, COUNT(*) as count')
                ->groupBy('source')
                ->get();

            $totalUsers = $usersBySource->sum('count');
            $usersBySource = $usersBySource->map(function ($item) use ($totalUsers) {
                $item->percentage = $totalUsers > 0 ? round(($item->count / $totalUsers) * 100, 1) : 0;
                return $item;
            });

            // Get subscription status distribution
            $subscriptionStatus = [
                [
                    'status' => 'active',
                    'count' => User::whereHas('currentSubscription', function ($q) {
                        $q->where('expires_at', '>', now())
                          ->where('is_active', true);
                    })->count(),
                ],
                [
                    'status' => 'expired',
                    'count' => User::where('expired', true)->count(),
                ],
                [
                    'status' => 'disabled',
                    'count' => User::whereNotNull('disabled_at')->count(),
                ],
            ];

            // Get user activity by hour
            $userActivityByHour = User::whereBetween('created_at', [$startDate, $endDate])
                ->selectRaw('HOUR(created_at) as hour, COUNT(*) as registrations')
                ->groupBy('hour')
                ->orderBy('hour')
                ->get();

            // Calculate growth metrics
            $previousPeriodStart = $startDate->copy()->sub($startDate->diffInDays($endDate) + 1, 'days');
            $previousPeriodEnd = $startDate->copy()->subDay();

            $currentUsers = $dailyRegistrations->sum('count');
            $previousUsers = User::whereBetween('created_at', [$previousPeriodStart, $previousPeriodEnd])->count();

            $userGrowthRate = $previousUsers > 0 ? (($currentUsers - $previousUsers) / $previousUsers) * 100 : 0;

            return response()->json([
                'success' => true,
                'data' => [
                    'period' => [
                        'start_date' => $startDate->toDateString(),
                        'end_date' => $endDate->toDateString(),
                        'type' => $period,
                    ],
                    'summary' => [
                        'total_new_users' => $currentUsers,
                        'previous_period_users' => $previousUsers,
                        'growth_rate' => round($userGrowthRate, 2),
                        'total_active_users' => User::whereHas('currentSubscription', function ($q) {
                            $q->where('expires_at', '>', now())
                              ->where('is_active', true);
                        })->count(),
                    ],
                    'charts' => [
                        'daily_registrations' => $dailyRegistrations,
                        'users_by_source' => $usersBySource,
                        'subscription_status' => $subscriptionStatus,
                        'hourly_activity' => $userActivityByHour,
                    ],
                ],
            ]);

        } catch (\Illuminate\Validation\ValidationException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $e->errors(),
            ], 422);

        } catch (\Exception $e) {
            Log::error("Exception getting users analytics", [
                'error' => $e->getMessage(),
                'request' => $request->all(),
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Internal server error',
            ], 500);
        }
    }
}
