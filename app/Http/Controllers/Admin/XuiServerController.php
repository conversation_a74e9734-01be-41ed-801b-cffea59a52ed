<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\XuiServer;
use App\Models\ServerPool;
use App\Services\XuiManagementService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class XuiServerController extends Controller
{
    public function __construct(
        private XuiManagementService $xuiManagementService
    ) {}

    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $servers = XuiServer::with(['serverPools', 'xuiInbounds'])
            ->orderBy('name')
            ->get()
            ->map(function (XuiServer $server) {
                return [
                    'id' => $server->id,
                    'name' => $server->name,
                    'host' => $server->host,
                    'port' => $server->port,
                    'web_base_path' => $server->web_base_path,
                    'is_active' => $server->is_active,
                    'is_online' => $server->isOnline(),
                    'last_sync_at' => $server->last_sync_at?->toISOString(),
                    'status_updated_at' => $server->status_updated_at?->toISOString(),
                    'clients_online_updated_at' => $server->clients_online_updated_at?->toISOString(),
                    'server_load' => $server->server_load,
                    'load_percentage' => $server->getLoadPercentageAttribute(),
                    'clients_total' => $server->getTotalClientsCount(),
                    'clients_online' => $server->getOnlineClientsCount(),
                    'server_pools' => $server->serverPools->map(fn($pool) => [
                        'id' => $pool->id,
                        'name' => $pool->name,
                    ]),
                    'inbounds_count' => $server->xuiInbounds->count(),
                    'inbounds' => $server->xuiInbounds->map(fn($inbound) => [
                        'id' => $inbound->id,
                        'inbound_id' => $inbound->inbound_id,
                        'remark' => $inbound->remark,
                        'protocol' => $inbound->protocol,
                        'port' => $inbound->port,
                        'enable' => $inbound->enable,
                        'clients_count' => count($inbound->getClientsFromSettings()),
                    ]),
                    'created_at' => $server->created_at->toISOString(),
                    'updated_at' => $server->updated_at->toISOString(),
                ];
            });

        return response()->json([
            'success' => true,
            'data' => $servers,
        ]);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'host' => 'required|string|max:255',
            'port' => 'required|integer|min:1|max:65535',
            'web_base_path' => 'required|string|max:255',
            'username' => 'required|string|max:255',
            'password' => 'required|string|max:255',
            'is_active' => 'boolean',
            'server_pools' => 'array',
            'server_pools.*' => 'exists:server_pools,id',
        ]);

        $server = XuiServer::create($validated);

        // Attach to server pools
        if (!empty($validated['server_pools'])) {
            $server->serverPools()->attach($validated['server_pools']);
        }

        $server->load(['serverPools', 'xuiInbounds']);

        return response()->json([
            'success' => true,
            'message' => 'Server created successfully.',
            'data' => [
                'id' => $server->id,
                'name' => $server->name,
                'host' => $server->host,
                'port' => $server->port,
                'web_base_path' => $server->web_base_path,
                'is_active' => $server->is_active,
                'server_pools' => $server->serverPools->map(fn($pool) => [
                    'id' => $pool->id,
                    'name' => $pool->name,
                ]),
                'created_at' => $server->created_at->toISOString(),
            ],
        ], 201);
    }

    /**
     * Display the specified resource.
     */
    public function show(XuiServer $server)
    {
        $server->load(['serverPools', 'xuiInbounds']);

        return response()->json([
            'success' => true,
            'data' => [
                'id' => $server->id,
                'name' => $server->name,
                'host' => $server->host,
                'port' => $server->port,
                'web_base_path' => $server->web_base_path,
                'username' => $server->username,
                'is_active' => $server->is_active,
                'last_sync_at' => $server->last_sync_at?->toISOString(),
                'status_updated_at' => $server->status_updated_at?->toISOString(),
                'server_load' => $server->server_load,
                'load_percentage' => $server->getLoadPercentageAttribute(),
                'server_status' => $server->server_status,
                'server_pools' => $server->serverPools->map(fn($pool) => [
                    'id' => $pool->id,
                    'name' => $pool->name,
                    'description' => $pool->description,
                ]),
                'inbounds' => $server->xuiInbounds->map(fn($inbound) => [
                    'id' => $inbound->id,
                    'inbound_id' => $inbound->inbound_id,
                    'remark' => $inbound->remark,
                    'protocol' => $inbound->protocol,
                    'port' => $inbound->port,
                    'enable' => $inbound->enable,
                    'up' => $inbound->up,
                    'down' => $inbound->down,
                    'total' => $inbound->total,
                    'expiry_time' => $inbound->expiry_time,
                ]),
                'created_at' => $server->created_at->toISOString(),
                'updated_at' => $server->updated_at->toISOString(),
            ],
        ]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, XuiServer $server)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'host' => 'required|string|max:255',
            'port' => 'required|integer|min:1|max:65535',
            'web_base_path' => 'required|string|max:255',
            'username' => 'required|string|max:255',
            'password' => 'required|string|max:255',
            'is_active' => 'boolean',
            'server_pools' => 'array',
            'server_pools.*' => 'exists:server_pools,id',
        ]);

        $server->update($validated);

        // Sync server pools
        $server->serverPools()->sync($validated['server_pools'] ?? []);

        $server->load(['serverPools', 'xuiInbounds']);

        return response()->json([
            'success' => true,
            'message' => 'Server updated successfully.',
            'data' => [
                'id' => $server->id,
                'name' => $server->name,
                'host' => $server->host,
                'port' => $server->port,
                'web_base_path' => $server->web_base_path,
                'is_active' => $server->is_active,
                'server_pools' => $server->serverPools->map(fn($pool) => [
                    'id' => $pool->id,
                    'name' => $pool->name,
                ]),
                'updated_at' => $server->updated_at->toISOString(),
            ],
        ]);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(XuiServer $server)
    {
        $server->serverPools()->detach();
        $server->delete();

        return response()->json([
            'success' => true,
            'message' => 'Server deleted successfully.',
        ]);
    }

    /**
     * Sync server inbounds.
     */
    public function syncInbounds(XuiServer $server)
    {
        try {
            $inbounds = $this->xuiManagementService->getInboundsList($server);

            if ($inbounds !== null) {
                // Update last sync time
                $server->update(['last_sync_at' => now()]);

                return response()->json([
                    'success' => true,
                    'message' => 'Server inbounds synced successfully.',
                    'data' => [
                        'inbounds_count' => count($inbounds),
                        'last_sync_at' => $server->last_sync_at->toISOString(),
                    ],
                ]);
            } else {
                return response()->json([
                    'success' => false,
                    'message' => 'Failed to sync server inbounds.',
                ], 400);
            }
        } catch (\Exception $e) {
            Log::error('Failed to sync server inbounds', [
                'server_id' => $server->id,
                'error' => $e->getMessage(),
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to sync server inbounds: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Test server connection.
     */
    public function testConnection(XuiServer $server)
    {
        try {
            $inbounds = $this->xuiManagementService->getInboundsList($server);

            if ($inbounds !== null) {
                return response()->json([
                    'success' => true,
                    'message' => 'Connection successful',
                    'inbounds_count' => count($inbounds),
                ]);
            } else {
                return response()->json([
                    'success' => false,
                    'message' => 'Connection failed',
                ]);
            }
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Connection failed: ' . $e->getMessage(),
            ]);
        }
    }

    /**
     * Refresh servers data.
     */
    public function refresh()
    {
        try {
            // Get fresh data from database
            $servers = XuiServer::with(['serverPools', 'xuiInbounds'])
                ->orderBy('name')
                ->get()
                ->map(function (XuiServer $server) {
                    return [
                        'id' => $server->id,
                        'name' => $server->name,
                        'host' => $server->host,
                        'port' => $server->port,
                        'web_base_path' => $server->web_base_path,
                        'is_active' => $server->is_active,
                        'last_sync_at' => $server->last_sync_at?->toISOString(),
                        'status_updated_at' => $server->status_updated_at?->toISOString(),
                        'clients_online_updated_at' => $server->clients_online_updated_at?->toISOString(),
                        'server_load' => $server->server_load,
                        'load_percentage' => $server->getLoadPercentageAttribute(),
                        'clients_total' => $server->getTotalClientsCount(),
                        'clients_online' => $server->getOnlineClientsCount(),
                        'server_pools' => $server->serverPools->map(fn($pool) => [
                            'id' => $pool->id,
                            'name' => $pool->name,
                        ]),
                        'inbounds_count' => $server->xuiInbounds->count(),
                        'inbounds' => $server->xuiInbounds->map(fn($inbound) => [
                            'id' => $inbound->id,
                            'inbound_id' => $inbound->inbound_id,
                            'remark' => $inbound->remark,
                            'protocol' => $inbound->protocol,
                            'port' => $inbound->port,
                            'enable' => $inbound->enable,
                            'clients_count' => count($inbound->getClientsFromSettings()),
                        ]),
                        'created_at' => $server->created_at->toISOString(),
                        'updated_at' => $server->updated_at->toISOString(),
                    ];
                });

            return response()->json([
                'success' => true,
                'data' => $servers,
                'message' => 'Servers data refreshed successfully',
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to refresh servers data', [
                'error' => $e->getMessage(),
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to refresh servers data: ' . $e->getMessage(),
            ], 500);
        }
    }
}
