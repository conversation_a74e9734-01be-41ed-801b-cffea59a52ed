<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\Order;
use App\Models\Payment;
use App\Models\SystemLog;
use App\Models\BackupHistory;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Response;

class ExportController extends Controller
{
    /**
     * Export users data to CSV.
     */
    public function exportUsers(Request $request)
    {
        try {
            $request->validate([
                'format' => 'string|in:csv,json',
                'filters' => 'array',
            ]);

            $format = $request->get('format', 'csv');
            $filters = $request->get('filters', []);

            $query = User::with(['serverPool', 'currentSubscription.subscriptionPlan']);

            // Apply filters
            if (isset($filters['source']) && $filters['source']) {
                $query->where('source', $filters['source']);
            }

            if (isset($filters['status']) && $filters['status']) {
                switch ($filters['status']) {
                    case 'active':
                        $query->whereHas('currentSubscription', function ($q) {
                            $q->where('expires_at', '>', now())
                              ->where('is_active', true);
                        });
                        break;
                    case 'expired':
                        $query->where('expired', true);
                        break;
                    case 'disabled':
                        $query->whereNotNull('disabled_at');
                        break;
                }
            }

            $users = $query->get();

            if ($format === 'json') {
                return $this->exportUsersAsJson($users);
            } else {
                return $this->exportUsersAsCsv($users);
            }

        } catch (\Exception $e) {
            Log::error("Exception exporting users", [
                'error' => $e->getMessage(),
                'request' => $request->all(),
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to export users data',
            ], 500);
        }
    }

    private function exportUsersAsCsv($users)
    {
        $filename = 'users_export_' . now()->format('Y-m-d_H-i-s') . '.csv';

        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => "attachment; filename=\"$filename\"",
        ];

        $callback = function() use ($users) {
            $file = fopen('php://output', 'w');

            // CSV headers
            fputcsv($file, [
                'ID', 'UUID', 'Name', 'Email', 'Telegram ID', 'Source',
                'Created At', 'Disabled At', 'Server Pool', 'Subscription Plan',
                'Subscription Expires', 'Traffic Used (GB)', 'Traffic Limit (GB)'
            ]);

            foreach ($users as $user) {
                $subscription = $user->currentSubscription;

                fputcsv($file, [
                    $user->id,
                    $user->uuid,
                    $user->name,
                    $user->email,
                    $user->tg_id,
                    $user->source,
                    $user->created_at->format('Y-m-d H:i:s'),
                    $user->disabled_at ? $user->disabled_at->format('Y-m-d H:i:s') : '',
                    $user->serverPool ? $user->serverPool->name : '',
                    $subscription && $subscription->subscriptionPlan ? $subscription->subscriptionPlan->name : '',
                    $subscription ? $subscription->expires_at->format('Y-m-d H:i:s') : '',
                    $subscription ? $subscription->traffic_used_gb : '',
                    $subscription ? $subscription->traffic_limit_gb : '',
                ]);
            }

            fclose($file);
        };

        return Response::stream($callback, 200, $headers);
    }

    private function exportUsersAsJson($users)
    {
        $filename = 'users_export_' . now()->format('Y-m-d_H-i-s') . '.json';

        $data = $users->map(function ($user) {
            $subscription = $user->currentSubscription;

            return [
                'id' => $user->id,
                'uuid' => $user->uuid,
                'name' => $user->name,
                'email' => $user->email,
                'tg_id' => $user->tg_id,
                'source' => $user->source,
                'created_at' => $user->created_at,
                'disabled_at' => $user->disabled_at,
                'server_pool' => $user->serverPool ? $user->serverPool->name : null,
                'subscription' => $subscription ? [
                    'plan_name' => $subscription->subscriptionPlan->name ?? null,
                    'expires_at' => $subscription->expires_at,
                    'traffic_used_gb' => $subscription->traffic_used_gb,
                    'traffic_limit_gb' => $subscription->traffic_limit_gb,
                ] : null,
            ];
        });

        return Response::json($data, 200, [
            'Content-Disposition' => "attachment; filename=\"$filename\"",
        ]);
    }

    /**
     * Export revenue data to CSV.
     */
    public function exportRevenue(Request $request)
    {
        try {
            $request->validate([
                'format' => 'string|in:csv,json',
                'start_date' => 'date',
                'end_date' => 'date|after_or_equal:start_date',
            ]);

            $format = $request->get('format', 'csv');
            $startDate = $request->start_date ? \Carbon\Carbon::parse($request->start_date) : now()->startOfMonth();
            $endDate = $request->end_date ? \Carbon\Carbon::parse($request->end_date) : now()->endOfMonth();

            $payments = Payment::where('status', 'completed')
                ->whereBetween('created_at', [$startDate, $endDate])
                ->with(['order.user', 'order.subscriptionPlan'])
                ->get();

            if ($format === 'json') {
                return $this->exportRevenueAsJson($payments);
            } else {
                return $this->exportRevenueAsCsv($payments);
            }

        } catch (\Exception $e) {
            Log::error("Exception exporting revenue", [
                'error' => $e->getMessage(),
                'request' => $request->all(),
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to export revenue data',
            ], 500);
        }
    }

    private function exportRevenueAsCsv($payments)
    {
        $filename = 'revenue_export_' . now()->format('Y-m-d_H-i-s') . '.csv';

        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => "attachment; filename=\"$filename\"",
        ];

        $callback = function() use ($payments) {
            $file = fopen('php://output', 'w');

            // CSV headers
            fputcsv($file, [
                'Payment ID', 'Order ID', 'User Email', 'User Source',
                'Subscription Plan', 'Amount (RUB)', 'Payment Method',
                'Status', 'Created At', 'Completed At'
            ]);

            foreach ($payments as $payment) {
                $order = $payment->order;
                $user = $order ? $order->user : null;
                $plan = $order ? $order->subscriptionPlan : null;

                fputcsv($file, [
                    $payment->id,
                    $payment->order_id,
                    $user ? $user->email : '',
                    $user ? $user->source : '',
                    $plan ? $plan->name : '',
                    $payment->amount_in_cents / 100,
                    $payment->payment_method,
                    $payment->status,
                    $payment->created_at->format('Y-m-d H:i:s'),
                    $payment->completed_at ? $payment->completed_at->format('Y-m-d H:i:s') : '',
                ]);
            }

            fclose($file);
        };

        return Response::stream($callback, 200, $headers);
    }

    private function exportRevenueAsJson($payments)
    {
        $filename = 'revenue_export_' . now()->format('Y-m-d_H-i-s') . '.json';

        $data = $payments->map(function ($payment) {
            $order = $payment->order;
            $user = $order ? $order->user : null;
            $plan = $order ? $order->subscriptionPlan : null;

            return [
                'payment_id' => $payment->id,
                'order_id' => $payment->order_id,
                'user_email' => $user ? $user->email : null,
                'user_source' => $user ? $user->source : null,
                'subscription_plan' => $plan ? $plan->name : null,
                'amount_rub' => $payment->amount_in_cents / 100,
                'payment_method' => $payment->payment_method,
                'status' => $payment->status,
                'created_at' => $payment->created_at,
                'completed_at' => $payment->completed_at,
            ];
        });

        return Response::json($data, 200, [
            'Content-Disposition' => "attachment; filename=\"$filename\"",
        ]);
    }
}
