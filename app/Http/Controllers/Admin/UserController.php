<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\ServerPool;
use App\Models\SubscriptionPlan;
use App\Services\ServerPoolService;
use App\Services\SubscriptionService;
use App\Services\UserSubscriptionService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Log;

class UserController extends Controller
{
    public function __construct(
        private SubscriptionService $subscriptionService,
        private UserSubscriptionService $userSubscriptionService,
        private ServerPoolService $serverPoolService,
    ) {}

    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $query = User::with(['serverPool', 'subscriptionPlan', 'userSubscriptions.subscriptionPlan']);

        // Search filters
        if ($request->filled('search')) {
            $search = $request->get('search');
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%")
                  ->orWhere('uuid', 'like', "%{$search}%")
                  ->orWhere('tg_id', 'like', "%{$search}%")
                  ->orWhere('old_client_id', 'like', "%{$search}%");
            });
        }

        if ($request->filled('server_pool_id')) {
            $query->where('server_pool_id', $request->get('server_pool_id'));
        }

        if ($request->filled('subscription_plan_id')) {
            $query->where('subscription_plan_id', $request->get('subscription_plan_id'));
        }

        if ($request->filled('status')) {
            $status = $request->get('status');
            if ($status === 'active') {
                $query->where('expired', false);
            } elseif ($status === 'expired') {
                $query->where('expired', true);
            }
        }

        $perPage = $request->get('per_page', 20);
        $users = $query->orderBy('created_at', 'desc')->paginate($perPage);

        $usersData = $users->getCollection()->map(function ($user) {
            $currentSubscription = $user->currentSubscription();

            // Получаем информацию о подписке и ключах
            $subscriptionContent = null;
            $vlessKeysCount = 0;
            $subscriptionUrl = null;

            try {
                $subscriptionContent = $this->subscriptionService->getSubscriptionByUuid($user->uuid);
                if ($subscriptionContent && $subscriptionContent->content) {
                    $vlessKeysCount = substr_count($subscriptionContent->content, 'vless://');
                }
                $subscriptionUrl = url("/subs/{$user->uuid}/modern");
            } catch (\Exception $e) {
                // Игнорируем ошибки получения подписки в списке
            }

            return [
                'id' => $user->id,
                'name' => $user->name,
                'email' => $user->email,
                'uuid' => $user->uuid,
                'old_client_id' => $user->old_client_id,
                'tg_id' => $user->tg_id,
                'comment' => $user->comment,
                'expired' => $user->expired,
                'total_gb' => $user->total_gb,
                'used_gb' => $user->used_gb,
                'up_traffic' => $user->up_traffic,
                'down_traffic' => $user->down_traffic,
                'expiry_time' => $user->expiry_time?->toISOString(),
                'server_pool' => $user->serverPool ? [
                    'id' => $user->serverPool->id,
                    'name' => $user->serverPool->name,
                ] : null,
                'subscription_plan' => $user->subscriptionPlan ? [
                    'id' => $user->subscriptionPlan->id,
                    'name' => $user->subscriptionPlan->name,
                    'traffic_limit_gb' => $user->subscriptionPlan->traffic_limit_gb,
                ] : null,
                'current_subscription' => $currentSubscription ? [
                    'id' => $currentSubscription->id,
                    'plan_name' => $currentSubscription->subscriptionPlan?->name,
                    'is_active' => $currentSubscription->is_active,
                    'traffic_limit_gb' => $currentSubscription->traffic_limit_gb,
                    'expires_at' => $currentSubscription->expires_at?->toISOString(),
                    'url' => $subscriptionUrl,
                ] : null,
                'vless_keys_count' => $vlessKeysCount,
                'subscription_url' => $subscriptionUrl,
                'has_active_subscription' => $user->hasActiveSubscription(),
                'created_at' => $user->created_at->toISOString(),
                'updated_at' => $user->updated_at->toISOString(),
            ];
        });

        return response()->json([
            'success' => true,
            'data' => $usersData,
            'pagination' => [
                'current_page' => $users->currentPage(),
                'last_page' => $users->lastPage(),
                'per_page' => $users->perPage(),
                'total' => $users->total(),
                'from' => $users->firstItem(),
                'to' => $users->lastItem(),
            ],
        ]);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|string|min:5|max:255|unique:users',
            // 'password' => 'required|string|min:8',
            'tg_id' => 'nullable|string|max:255',
            'old_client_id' => 'nullable|string|max:255',
            'comment' => 'nullable|string',
            'server_pool_id' => 'nullable|exists:server_pools,id',
            'subscription_plan_id' => 'required|exists:subscription_plans,id', // Обязательное поле
            'total_gb' => 'nullable|integer|min:0',
            'expiry_time' => 'nullable|date',
            'expired' => 'boolean',
        ]);

        // $validated['password'] = Hash::make($validated['password']);
        $validated['password'] = null;

        // Создаем подписку для пользователя
        $subscription = null;
        $subscriptionUrl = null;
        $vlessKeys = [];

        try {

            // Создание пользователя
            $user = User::create($validated);
            $user->load(['serverPool', 'subscriptionPlan']);

            // Получаем план подписки
            $subscriptionPlan = SubscriptionPlan::find($validated['subscription_plan_id']);
            $serverPool = $this->serverPoolService->autoAssignUserToPool($user);
            // $serverPool = $validated['server_pool_id'] ? ServerPool::find($validated['server_pool_id']) : null;

            // Создаем подписку
            $subscription = $this->userSubscriptionService->createSubscription(
                $user,
                $subscriptionPlan,
                $serverPool
            );

            if ($subscription) {
                // Генерируем URL подписки
                $subscriptionUrl = url("/subs/{$user->uuid}/modern");

                // Получаем VLESS ключи
                $subscriptionContent = $this->subscriptionService->getSubscriptionByUuid($user->uuid);
                if ($subscriptionContent && $subscriptionContent->content) {
                    // Извлекаем VLESS ключи из контента
                    preg_match_all('/vless:\/\/[^\s\n\r]+/', $subscriptionContent->content, $matches);
                    $vlessKeys = $matches[0] ?? [];
                }
            }

        } catch (\Exception $e) {
            Log::error('Failed to create subscription for user', [
                'user_id' => $user->id,
                'error' => $e->getMessage(),
            ]);
        }

        return response()->json([
            'success' => true,
            'message' => 'User created successfully.',
            'data' => [
                'id' => $user->id,
                'name' => $user->name,
                'email' => $user->email,
                'uuid' => $user->uuid,
                'old_client_id' => $user->old_client_id,
                'tg_id' => $user->tg_id,
                'comment' => $user->comment,
                'server_pool' => $user->serverPool ? [
                    'id' => $user->serverPool->id,
                    'name' => $user->serverPool->name,
                ] : null,
                'subscription_plan' => $user->subscriptionPlan ? [
                    'id' => $user->subscriptionPlan->id,
                    'name' => $user->subscriptionPlan->name,
                ] : null,
                'subscription' => $subscription ? [
                    'id' => $subscription->id,
                    'is_active' => $subscription->is_active,
                    'expires_at' => $subscription->expires_at?->toISOString(),
                    'url' => $subscriptionUrl,
                ] : null,
                'vless_keys' => $vlessKeys,
                'vless_keys_count' => count($vlessKeys),
                'created_at' => $user->created_at->toISOString(),
            ],
        ], 201);
    }

    /**
     * Display the specified resource.
     */
    public function show(User $user)
    {
        $user->load([
            'serverPool',
            'subscriptionPlan',
            'userSubscriptions.subscriptionPlan',
            'userSubscriptions.serverPool',
            'xuiClients.xuiServer',
            'xuiClients.xuiInbound'
        ]);

        // Get subscription content
        $subscription = $this->subscriptionService->getSubscriptionByUuid($user->uuid);
        $currentSubscription = $user->currentSubscription();

        return response()->json([
            'success' => true,
            'data' => [
                'id' => $user->id,
                'name' => $user->name,
                'email' => $user->email,
                'uuid' => $user->uuid,
                'old_client_id' => $user->old_client_id,
                'tg_id' => $user->tg_id,
                'comment' => $user->comment,
                'expired' => $user->expired,
                'total_gb' => $user->total_gb,
                'used_gb' => $user->used_gb,
                'up_traffic' => $user->up_traffic,
                'down_traffic' => $user->down_traffic,
                'expiry_time' => $user->expiry_time?->toISOString(),
                'traffic_reset_at' => $user->traffic_reset_at?->toISOString(),
                'disabled_at' => $user->disabled_at?->toISOString(),
                'demo_until' => $user->demo_until?->toISOString(),
                'server_pool' => $user->serverPool ? [
                    'id' => $user->serverPool->id,
                    'name' => $user->serverPool->name,
                    'description' => $user->serverPool->description,
                ] : null,
                'subscription_plan' => $user->subscriptionPlan ? [
                    'id' => $user->subscriptionPlan->id,
                    'name' => $user->subscriptionPlan->name,
                    'price' => $user->subscriptionPlan->price,
                ] : null,
                'current_subscription' => $currentSubscription ? [
                    'id' => $currentSubscription->id,
                    'plan_name' => $currentSubscription->subscriptionPlan?->name,
                    'is_active' => $currentSubscription->is_active,
                    'expires_at' => $currentSubscription->expires_at?->toISOString(),
                    'traffic_limit_gb' => $currentSubscription->traffic_limit_gb,
                    'server_pool' => $currentSubscription->serverPool ? [
                        'id' => $currentSubscription->serverPool->id,
                        'name' => $currentSubscription->serverPool->name,
                    ] : null,
                ] : null,
                'subscriptions' => $user->userSubscriptions->map(fn($sub) => [
                    'id' => $sub->id,
                    'plan_name' => $sub->subscriptionPlan?->name,
                    'is_active' => $sub->is_active,
                    'expires_at' => $sub->expires_at?->toISOString(),
                    'traffic_limit_gb' => $sub->traffic_limit_gb,
                    'created_at' => $sub->created_at->toISOString(),
                ]),
                'xui_clients' => $user->xuiClients->map(fn($client) => [
                    'id' => $client->id,
                    'email' => $client->email,
                    'enable' => $client->enable,
                    'up' => $client->up,
                    'down' => $client->down,
                    'total' => $client->total,
                    'expiry_time' => $client->expiry_time,
                    'server' => [
                        'id' => $client->xuiServer->id,
                        'name' => $client->xuiServer->name,
                        'host' => $client->xuiServer->host,
                    ],
                    'inbound' => $client->xuiInbound ? [
                        'id' => $client->xuiInbound->id,
                        'remark' => $client->xuiInbound->remark,
                        'protocol' => $client->xuiInbound->protocol,
                        'port' => $client->xuiInbound->port,
                    ] : null,
                ]),
                'subscription_content' => $subscription ? [
                    'content' => $subscription->content,
                    'keys_count' => substr_count($subscription->content, 'vless://'),
                    'upload' => $subscription->upload,
                    'download' => $subscription->download,
                    'total' => $subscription->total,
                ] : null,
                'has_active_subscription' => $user->hasActiveSubscription(),
                'created_at' => $user->created_at->toISOString(),
                'updated_at' => $user->updated_at->toISOString(),
            ],
        ]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, User $user)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:users,email,' . $user->id,
            'password' => 'nullable|string|min:8',
            'tg_id' => 'nullable|string|max:255',
            'old_client_id' => 'nullable|string|max:255',
            'comment' => 'nullable|string',
            'server_pool_id' => 'nullable|exists:server_pools,id',
            'subscription_plan_id' => 'nullable|exists:subscription_plans,id',
            'total_gb' => 'nullable|integer|min:0',
            'expiry_time' => 'nullable|date',
            'expired' => 'boolean',
        ]);

        if (!empty($validated['password'])) {
            $validated['password'] = Hash::make($validated['password']);
        } else {
            unset($validated['password']);
        }

        $user->update($validated);
        $user->load(['serverPool', 'subscriptionPlan']);

        return response()->json([
            'success' => true,
            'message' => 'User updated successfully.',
            'data' => [
                'id' => $user->id,
                'name' => $user->name,
                'email' => $user->email,
                'uuid' => $user->uuid,
                'old_client_id' => $user->old_client_id,
                'tg_id' => $user->tg_id,
                'comment' => $user->comment,
                'expired' => $user->expired,
                'server_pool' => $user->serverPool ? [
                    'id' => $user->serverPool->id,
                    'name' => $user->serverPool->name,
                ] : null,
                'subscription_plan' => $user->subscriptionPlan ? [
                    'id' => $user->subscriptionPlan->id,
                    'name' => $user->subscriptionPlan->name,
                ] : null,
                'updated_at' => $user->updated_at->toISOString(),
            ],
        ]);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(User $user)
    {
        // Remove user clients from servers
        try {
            $this->subscriptionService->removeUserClientsFromServers($user);
        } catch (\Exception) {
            // Log error but continue with deletion
        }

        $user->delete();

        return response()->json([
            'success' => true,
            'message' => 'User deleted successfully.',
        ]);
    }

    /**
     * Update user traffic from servers.
     */
    public function updateTraffic(User $user)
    {
        try {
            $this->subscriptionService->updateUserTrafficFromServers($user);

            // Reload user data
            $user->refresh();

            return response()->json([
                'success' => true,
                'message' => 'User traffic updated successfully.',
                'data' => [
                    'up_traffic' => $user->up_traffic,
                    'down_traffic' => $user->down_traffic,
                    'used_gb' => $user->used_gb,
                    'updated_at' => $user->updated_at->toISOString(),
                ],
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update user traffic: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Ensure user clients on servers.
     */
    public function ensureClients(User $user)
    {
        try {
            $success = $this->subscriptionService->ensureUserClientsOnServers($user);

            if ($success) {
                return response()->json([
                    'success' => true,
                    'message' => 'User clients ensured on servers.',
                ]);
            } else {
                return response()->json([
                    'success' => false,
                    'message' => 'Failed to ensure user clients on servers.',
                ], 400);
            }
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to ensure user clients: ' . $e->getMessage(),
            ], 500);
        }
    }
}
