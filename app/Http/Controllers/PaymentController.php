<?php

namespace App\Http\Controllers;

use App\Models\User;
use App\Models\Order;
use App\Models\SubscriptionPlan;
use App\Services\PaymentService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Log;

class PaymentController extends Controller
{
    public function __construct(
        private PaymentService $paymentService
    ) {}

    /**
     * Create order and initiate payment.
     */
    public function createPayment(Request $request): JsonResponse
    {
        try {
            $request->validate([
                'user_uuid' => 'required|string',
                'plan_id' => 'required|integer|exists:subscription_plans,id',
                'method' => 'required|string|in:tbank,manual,balance',
            ]);

            $user = User::findByUuid($request->user_uuid);
            if (!$user) {
                return response()->json([
                    'success' => false,
                    'message' => 'User not found',
                ], 404);
            }

            $plan = SubscriptionPlan::active()->find($request->plan_id);
            if (!$plan) {
                return response()->json([
                    'success' => false,
                    'message' => 'Subscription plan not found or inactive',
                ], 404);
            }

            // Create order
            $order = $this->paymentService->createOrder($user, $plan);
            if (!$order) {
                return response()->json([
                    'success' => false,
                    'message' => 'Failed to create order',
                ], 500);
            }

            // Process payment
            $payment = $this->paymentService->processPayment(
                $order,
                $request->method,
                $request->only(['note', 'admin_id', 'transaction_id', 'system'])
            );

            if (!$payment) {
                return response()->json([
                    'success' => false,
                    'message' => 'Failed to process payment',
                ], 500);
            }

            $response = [
                'success' => true,
                'order_id' => $order->id,
                'payment_id' => $payment->id,
                'amount' => $order->getAmount(),
                'currency' => $order->currency,
                'status' => $payment->status,
            ];

            // Add payment URL for T-Bank
            if ($request->method === 'tbank' && $payment->payment_url) {
                $response['payment_url'] = $payment->payment_url;
            }

            return response()->json($response);

        } catch (\Illuminate\Validation\ValidationException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $e->errors(),
            ], 422);

        } catch (\Exception $e) {
            Log::error("Exception creating payment", [
                'request' => $request->all(),
                'error' => $e->getMessage(),
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Internal server error',
            ], 500);
        }
    }

    /**
     * Get payment status.
     */
    public function getPaymentStatus(int $orderId): JsonResponse
    {
        try {
            $order = Order::find($orderId);
            if (!$order) {
                return response()->json([
                    'success' => false,
                    'message' => 'Order not found',
                ], 404);
            }

            $payment = $order->getSuccessfulPayment() ?? $order->payments()->latest()->first();

            return response()->json([
                'success' => true,
                'order_id' => $order->id,
                'order_status' => $order->status,
                'payment_id' => $payment?->id,
                'payment_status' => $payment?->status,
                'amount' => $order->getAmount(),
                'currency' => $order->currency,
                'created_at' => $order->created_at,
                'expires_at' => $order->expires_at,
            ]);

        } catch (\Exception $e) {
            Log::error("Exception getting payment status", [
                'order_id' => $orderId,
                'error' => $e->getMessage(),
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Internal server error',
            ], 500);
        }
    }

    /**
     * Payment success page.
     */
    public function paymentSuccess(int $orderId)
    {
        $order = Order::find($orderId);
        if (!$order) {
            abort(404, 'Order not found');
        }

        return view('payment.success', [
            'order' => $order,
            'user' => $order->user,
            'plan' => $order->subscriptionPlan,
        ]);
    }

    /**
     * Payment failure page.
     */
    public function paymentFail(int $orderId)
    {
        $order = Order::find($orderId);
        if (!$order) {
            abort(404, 'Order not found');
        }

        return view('payment.fail', [
            'order' => $order,
            'user' => $order->user,
            'plan' => $order->subscriptionPlan,
        ]);
    }
}
