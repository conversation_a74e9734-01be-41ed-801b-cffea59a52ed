<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\SubscriptionPlan;
use App\Models\UserSubscription;
use App\Services\UserSubscriptionService;
use App\Services\SubscriptionService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;
use Carbon\Carbon;

class SubscriptionApiController extends Controller
{
    public function __construct(
        private UserSubscriptionService $userSubscriptionService,
        private SubscriptionService $subscriptionService
    ) {}

    /**
     * Find user by one of the provided identifiers.
     *
     * @param string|null $tgId
     * @param string|null $email
     * @param string|null $clientId
     * @return User|null
     */
    private function findUserByIdentifier(?string $uuid, ?string $tgId, ?string $email, ?string $clientId): ?User
    {
        // dd($uuid, $tgId, $email, $clientId);
        // Try to find by uuid
        if ($uuid) {
            $user = User::where('uuid', $uuid)->first();
            if ($user) {
                return $user;
            }
        }

        // Try to find by tg_id first
        if ($tgId) {
            $user = User::where('tg_id', $tgId)->first();
            if ($user) {
                return $user;
            }
            $user = User::where('email', "client{$tgId}")->first();
            if ($user) {
                return $user;
            }
        }

        // Try to find by email
        if ($email) {
            $user = User::where('email', $email)->first();
            if ($user) {
                return $user;
            }
        }

        // Try to find by client_id (assuming it's stored in email field with "client" prefix)
        if ($clientId) {
            $emailFromClientId = "client{$clientId}";
            $user = User::where('email', $emailFromClientId)->first();
            if ($user) {
                return $user;
            }
        }

        return null;
    }

    /**
     * Create new user with subscription.
     * If demo, then create demo subscription (by demo $plan->is_demo) and do not use expiration.
     * If not demo, then create regular subscription and use expiration if provided.
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function createUser(Request $request): JsonResponse
    {
        try {
            // Validate request
            $validator = Validator::make($request->all(), [
                'tg_id' => 'required|string|max:255',
                'source' => 'nullable|string|max:255',
                'external_id' => 'nullable|string|max:255',
                'comment' => 'nullable|string|max:500',
                'demo' => ['nullable', 'string', function ($attribute, $value, $fail) {
                    if (!in_array($value, ['true', 'false', '1', '0'], true)) {
                        $fail($attribute.' must be boolean-like string (true, false, 1, 0).');
                    }
                }],
                'expiration' => ['integer', 'min:1', function ($attribute, $value, $fail) use ($request) {
                    $demo = $request->input('demo');
                    // expiration обязателен, если demo НЕ true и НЕ 1
                    if (!in_array($demo, ['true', '1'], true) && ($value === null || $value === '')) {
                        $fail($attribute . ' is required when demo is not true or 1.');
                    }
                }],
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 400);
            }

            $tgId = $request->input('tg_id', null);
            $comment = $request->input('comment');
            $source = $request->input('source');
            $externalId = $request->input('external_id');
            $isDemo = $request->boolean('demo', false);
            $expiration = $request->input('expiration');
            $email = "";

            if ($source && $externalId) {
                $email = "{$externalId}@{$source}";
            } else if ($source && !$externalId) {
                $email = "tg{$tgId}@{$source}";
            }

            // Check if user already exists
            $existingUser = User::where('tg_id', $tgId)
                ->orWhere('email', $email)
                ->first();

            if ($existingUser) {
                return response()->json([
                    'success' => false,
                    'message' => 'User already exists',
                    'data' => [
                        'existing_user' => [
                            'id' => $existingUser->id,
                            'uuid' => $existingUser->uuid,
                            'email' => $existingUser->email,
                            'tg_id' => $existingUser->tg_id,
                        ]
                    ]
                ], 409);
            }

            DB::beginTransaction();

            try {
                // Create user
                $user = User::create([
                    'uuid' => Str::uuid(),
                    'name' => $email ?: "client{$tgId}",
                    'email' => $email ?: "tg{$tgId}",
                    'tg_id' => $tgId,
                    'comment' => $comment,
                    'source' => $source,
                    'external_id' => $externalId,
                ]);

                // Find or create custom subscription plan
                $subscriptionPlan = SubscriptionPlan::where('is_custom', true)->first();
                if (!$subscriptionPlan) {
                    // Create default custom plan if none exists
                    throw new \Exception('Custom subscription plan not found');
                }

                // Create subscription with custom expiration if provided
                $specificExpiryDate = null;
                if (!$isDemo && $expiration) {
                    $specificExpiryDate = Carbon::createFromTimestamp($expiration);
                }

                $subscription = $this->userSubscriptionService->createSubscription(
                    $user,
                    $subscriptionPlan,
                    null, // auto-assign pool
                    $specificExpiryDate
                );

                if (!$subscription) {
                    throw new \Exception('Failed to create subscription');
                }

                // Generate subscription link
                $subscriptionLink = config('app.url') . '/subs/' . $user->uuid;

                DB::commit();

                Log::info('User creation completed', [
                    'user_id' => $user->id,
                    'subscription_id' => $subscription->id,
                    'is_demo' => $isDemo,
                    'expiration' => $expiration,
                ]);

                return response()->json([
                    'success' => true,
                    'message' => 'User created successfully',
                    'data' => [
                        'user' => [
                            'id' => $user->id,
                            'uuid' => $user->uuid,
                            'email' => $user->email,
                            'tg_id' => $user->tg_id,
                            'created_at' => $user->created_at,
                            'updated_at' => $user->updated_at,
                        ],
                        'subscription' => [
                            'id' => $subscription->id,
                            'plan_name' => $subscription->subscriptionPlan->name,
                            'started_at' => $subscription->started_at,
                            'expires_at' => $subscription->expires_at,
                            'is_demo' => $subscription->subscriptionPlan->is_demo,
                            'is_custom' => $subscription->subscriptionPlan->is_custom,
                            'is_expired' => $subscription->isExpired(),
                            'traffic_limit_gb' => $subscription->traffic_limit_gb,
                            'is_active' => $subscription->is_active,
                        ],
                        'subscription_link' => $subscriptionLink,
                        'is_demo' => $isDemo,
                        'expiration' => $expiration ? Carbon::createFromTimestamp($expiration)->toISOString() : null,
                    ]
                ], 201);

            } catch (\Exception $e) {
                DB::rollBack();
                throw $e;
            }

        } catch (\Exception $e) {
            Log::error('Error creating user', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'request' => $request->all()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Internal server error'
            ], 500);
        }
    }

    /**
     * Get API information.
     *
     * @return JsonResponse
     */
    public function info(): JsonResponse
    {
        return response()->json([
            'success' => true,
            'data' => [
                'api_version' => '1.0',
                'endpoints' => [
                    'subscription_link' => [
                        'method' => 'POST',
                        'url' => '/api/subscription/link',
                        'description' => 'Get subscription link by user identifier',
                        'parameters' => [
                            'tg_id' => 'Telegram ID (optional)',
                            'email' => 'User email (optional)',
                            'client_id' => 'Client ID (optional)',
                        ],
                        'note' => 'At least one parameter must be provided'
                    ],
                    'create_user' => [
                        'method' => 'POST',
                        'url' => '/api/user/create',
                        'description' => 'Create new user with XUI clients',
                        'parameters' => [
                            'tg_id' => 'Telegram ID (required)',
                            'comment' => 'User comment (required)',
                            'demo' => 'Demo access flag (optional, default: false)',
                            'expiration' => 'Unix timestamp for expiration (optional)',
                            'source' => 'Source string (optional). Ex: telegram, leadteh, api',
                            'external_id' => 'ID from external service like leadteh, fb',
                        ],
                        'note' => 'If source and external_id are passed then email will be generated as external_id@source',
                    ]
                ]
            ]
        ]);
    }

    /**
     * Get user statistics by tg_id, uuid, or email
     */
    public function getUserStats(Request $request)
    {
        try {
            // Validate request - at least one identifier is required
            $request->validate([
                'tg_id' => 'nullable|string',
                'uuid' => 'nullable|string',
                'email' => 'nullable|string',
                'old_client_id' => 'nullable|string',
            ]);

            // Check that at least one identifier is provided
            if (!$request->tg_id && !$request->uuid && !$request->email) {
                return response()->json([
                    'success' => false,
                    'message' => 'At least one of tg_id, uuid, email or old_client_id is required',
                ], 422);
            }

            // Find user by provided identifier
            $user = null;
            if ($request->tg_id) {
                $user = User::where('tg_id', $request->tg_id)->first();
            } elseif ($request->uuid) {
                $user = User::where('uuid', $request->uuid)->first();
            } elseif ($request->email) {
                $user = User::where('email', $request->email)->first();
            } elseif ($request->old_client_id) {
                $user = User::where('old_client_id', $request->old_client_id)->first();
            }

            if (!$user) {
                return response()->json([
                    'success' => false,
                    'message' => 'User not found',
                ], 404);
            }

            // Get current subscription
            $currentSubscription = $user->getCurrentSubscription();

            // Calculate traffic statistics
            $usedTraffic = $user->up_traffic + $user->down_traffic;
            $totalLimit = $user->total_gb;
            $remainingTraffic = $totalLimit > 0 ? max(0, $totalLimit - $usedTraffic) : 0;
            $usagePercentage = $totalLimit > 0 ? min(100, ($usedTraffic / $totalLimit) * 100) : 0;

            // Calculate time-based statistics
            $isExpired = $user->expiry_time && $user->expiry_time->isPast();
            $daysUntilExpiry = $user->expiry_time ? now()->diffInDays($user->expiry_time, false) : null;
            $daysSinceCreation = $user->created_at->diffInDays(now());

            // Calculate daily average usage
            $daysActive = max(1, $daysSinceCreation);
            $dailyAverage = $usedTraffic / $daysActive;

            // Determine status
            $isLimited = $totalLimit > 0 && $usedTraffic >= $totalLimit;
            $status = $isLimited ? 'limited' : ($isExpired ? 'expired' : 'active');

            // Get server information
            $servers = [];
            if ($currentSubscription && $currentSubscription->serverPool) {
                $servers = $currentSubscription->serverPool->servers()
                    ->where('xui_servers.is_active', true)
                    ->select([
                        'xui_servers.id',
                        'xui_servers.name',
                        'xui_servers.host',
                        'xui_servers.port',
                        'xui_servers.is_active',
                    ])
                    ->get()
                    ->toArray();
            }

            $canConnect = !$isExpired && !$isLimited && !$user->disabled_at;
            $isDemo = $currentSubscription ? $currentSubscription->subscriptionPlan->is_demo : false;

            $statsData = [
                'server_time' => now()->toISOString(),
                // Basic user info
                'tg_id' => $user->tg_id,
                'uuid' => $user->uuid,
                'email' => $user->email,
                'comment' => $user->comment,
                'created_at' => $user->created_at->toISOString(),
                'updated_at' => $user->updated_at->toISOString(),

                // Server and connection info
                'servers' => $servers,
                'enable' => $canConnect,
                'can_connect' => $canConnect,

                // Traffic statistics (from users table)
                'up' => $user->up_traffic,
                'down' => $user->down_traffic,
                'total' => $totalLimit,
                'used_traffic' => $usedTraffic,
                'remaining_traffic' => $remainingTraffic,
                'usage_percentage' => round($usagePercentage, 2),
                'daily_average' => round($dailyAverage),

                // Time and expiry info
                'expiry_time' => $user->expiry_time ? $user->expiry_time->timestamp * 1000 : 0, // milliseconds
                'days_until_expiry' => $daysUntilExpiry,
                'is_expired' => $isExpired,
                'expired' => $user->expired,
                'disabled_at' => $user->disabled_at?->toISOString(),

                // Demo and status info
                'is_demo' => $isDemo,
                'status' => $status,
                'needs_renewal' => $isExpired || ($daysUntilExpiry !== null && $daysUntilExpiry <= 3),

                // Subscription info
                'subscription' => $currentSubscription ? [
                    'id' => $currentSubscription->id,
                    'plan_name' => $currentSubscription->subscriptionPlan->name,
                    'started_at' => $currentSubscription->started_at->toISOString(),
                    'expires_at' => $currentSubscription->expires_at->toISOString(),
                    'traffic_limit_gb' => $currentSubscription->traffic_limit_gb,
                    'traffic_used_gb' => $currentSubscription->traffic_used_gb,
                    'is_active' => $currentSubscription->is_active,
                    'is_expired' => $currentSubscription->isExpired(),
                    'remaining_days' => $currentSubscription->getRemainingDays(),
                ] : null,

                // Additional info
                'days_since_creation' => $daysSinceCreation,
                'subscription_link' => config('app.url') . '/subs/' . $user->uuid,
            ];

            Log::info('User stats retrieved', [
                'user_id' => $user->id,
                'tg_id' => $user->tg_id,
                'status' => $status,
                'used_traffic_gb' => round($usedTraffic / 1024 / 1024 / 1024, 2),
            ]);

            return response()->json([
                'success' => true,
                'data' => $statsData,
            ]);

        } catch (\Illuminate\Validation\ValidationException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $e->errors(),
            ], 422);
        } catch (\Exception $e) {
            Log::error('Failed to get user stats', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to get user stats',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Extend user subscription.
     */
    public function extendSubscription(Request $request): JsonResponse
    {
        try {
            $request->validate([
                'tg_id' => 'nullable|string',
                'uuid' => 'nullable|string',
                'email' => 'nullable|string',
                'old_client_id' => 'nullable|string',
                'expiration' => 'nullable|integer|min:0',
                'days' => 'nullable|integer|min:1',
                'comment' => 'nullable|string|max:1000',
                'source' => 'nullable|string|max:100',
                'plan_id' => 'nullable|integer|exists:subscription_plans,id',
            ]);

            // Check that either expiration or days is provided
            $expiration = $request->input('expiration');
            $days = $request->input('days');

            if (!$expiration && !$days) {
                return response()->json([
                    'success' => false,
                    'message' => 'Either expiration timestamp or days must be provided',
                ], 422);
            }

            // Check that at least one identifier is provided
            if (!$request->tg_id && !$request->uuid && !$request->email && !$request->old_client_id) {
                return response()->json([
                    'success' => false,
                    'message' => 'At least one of tg_id, uuid, or email is required',
                ], 422);
            }

            // Find user by provided identifier
            $user = null;
            if ($request->tg_id) {
                $user = User::where('tg_id', $request->tg_id)->first();
            } elseif ($request->uuid) {
                $user = User::where('uuid', $request->uuid)->first();
            } elseif ($request->email) {
                $user = User::where('email', $request->email)->first();
            } elseif ($request->old_client_id) {
                $user = User::where('old_client_id', $request->old_client_id)->first();
            }

            if (!$user) {
                return response()->json([
                    'success' => false,
                    'message' => 'User not found',
                ], 404);
            }

            DB::beginTransaction();

            try {
                // Get current subscription
                $currentSubscription = $user->getCurrentSubscription();

                $oldExpiryTime = null;
                $startDate = null;
                $newExpiryTime = null;

                // Calculate new expiry time
                if ($expiration) {
                    // Use specific timestamp
                    $newExpiryTime = $expiration > 9999999999
                        ? Carbon::createFromTimestampMs($expiration)
                        : Carbon::createFromTimestamp($expiration);
                } elseif ($days) {
                    // Extend by days from current expiry
                    $newExpiryTime = now()->addDays($days);
                }

                // if subscription is active, extend it
                if ($currentSubscription && $currentSubscription->isActive())
                {
                    $oldExpiryTime = $currentSubscription->expires_at;
                    $startDate = $oldExpiryTime->isFuture() ? $oldExpiryTime : now();
                    $newExpiryTime = $startDate->copy()->addDays($days);

                    $currentSubscription = $this->userSubscriptionService->extendSubscription(
                        $currentSubscription,
                        $newExpiryTime
                    );
                }
                // if subscription is expired, create new one
                else if (! $currentSubscription || ! $currentSubscription->isActive())
                {
                    if ($currentSubscription) {
                        // Deactivate current subscription
                        $currentSubscription->deactivate();
                        $plan = $currentSubscription->subscriptionPlan;
                    } else {
                        $plan = SubscriptionPlan::getCustomPlan();
                    }

                    // Create new subscription
                    $currentSubscription = $this->userSubscriptionService->createSubscription(
                        $user,
                        $plan,
                        null,
                        $newExpiryTime
                    );
                }

                // Update user's expiry time for backward compatibility
                $user->update([
                    'expiry_time' => $newExpiryTime,
                    'expired' => false,
                ]);

                DB::commit();

                Log::info('Subscription extension completed', [
                    'user_id' => $user->id,
                    'uuid' => $user->uuid,
                    'tg_id' => $user->tg_id,
                    'subscription_id' => $currentSubscription->id,
                    'old_expiry_time' => $oldExpiryTime->toISOString(),
                    'new_expiry_time' => $newExpiryTime->toISOString(),
                    'extension_days' => $days,
                    'extension_timestamp' => $expiration,
                ]);

                return response()->json([
                    'success' => true,
                    'message' => 'Subscription extended successfully',
                    'data' => [
                        'user' => [
                            'id' => $user->id,
                            'uuid' => $user->uuid,
                            'email' => $user->email,
                            'tg_id' => $user->tg_id,
                            'updated_at' => $user->fresh()->updated_at,
                        ],
                        'subscription' => [
                            'id' => $currentSubscription->id,
                            'plan_name' => $currentSubscription->subscriptionPlan->name,
                            'started_at' => $currentSubscription->started_at->toISOString(),
                            'expires_at' => $currentSubscription->expires_at->toISOString(),
                            'traffic_limit_gb' => $currentSubscription->traffic_limit_gb,
                            'traffic_used_gb' => $currentSubscription->traffic_used_gb,
                            'is_active' => $currentSubscription->is_active,
                            'is_expired' => $currentSubscription->isExpired(),
                            'remaining_days' => $currentSubscription->getRemainingDays(),
                        ],
                        'old_expiry_time' => $oldExpiryTime->toISOString(),
                        'new_expiry_time' => $newExpiryTime->toISOString(),
                        'extension_days' => $days,
                        'extension_timestamp' => $expiration,
                    ]
                ]);

            } catch (\Exception $e) {
                DB::rollBack();
                throw $e;
            }

        } catch (\Illuminate\Validation\ValidationException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $e->errors(),
            ], 422);
        } catch (\Exception $e) {
            Log::error('Failed to extend subscription', [
                'request_data' => $request->all(),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to extend subscription',
                'error' => $e->getMessage(),
            ], 500);
        }
    }
}
