<?php

namespace App\Jobs;

use App\Events\UserSubscriptionExpired;
use App\Models\UserSubscription;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class ExpireUserSubscriptionsJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * The number of times the job may be attempted.
     */
    public int $tries = 3;

    /**
     * The number of seconds to wait before retrying the job.
     */
    public int $backoff = 60;

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        $startTime = microtime(true);
        $jobId = uniqid('expire_job_');

        Log::info("ExpireUserSubscriptionsJob: Starting to process expired subscriptions", [
            'job_id' => $jobId,
            'started_at' => now()->toISOString(),
        ]);

        try {
            DB::beginTransaction();

            // Find all expired subscriptions that are still active
            $expiredSubscriptions = UserSubscription::where('is_active', true)
                ->where('expires_at', '<=', now())
                ->with(['user', 'subscriptionPlan'])
                ->get();

            $processedCount = 0;
            $errorCount = 0;

            Log::info("ExpireUserSubscriptionsJob: Found expired subscriptions", [
                'job_id' => $jobId,
                'count' => $expiredSubscriptions->count(),
            ]);

            foreach ($expiredSubscriptions as $subscription) {
                try {
                    $this->expireSubscription($subscription, $jobId);
                    $processedCount++;
                } catch (\Exception $e) {
                    $errorCount++;
                    Log::error("ExpireUserSubscriptionsJob: Failed to expire subscription", [
                        'job_id' => $jobId,
                        'subscription_id' => $subscription->id,
                        'user_id' => $subscription->user_id,
                        'error' => $e->getMessage(),
                    ]);
                }
            }

            DB::commit();

            $executionTime = round((microtime(true) - $startTime) * 1000, 2);

            Log::info("ExpireUserSubscriptionsJob: Completed processing expired subscriptions", [
                'job_id' => $jobId,
                'total_found' => $expiredSubscriptions->count(),
                'processed' => $processedCount,
                'errors' => $errorCount,
                'execution_time_ms' => $executionTime,
                'completed_at' => now()->toISOString(),
            ]);

        } catch (\Exception $e) {
            DB::rollBack();
            
            Log::error("ExpireUserSubscriptionsJob: Critical error during processing", [
                'job_id' => $jobId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            throw $e;
        }
    }

    /**
     * Expire a single subscription.
     */
    private function expireSubscription(UserSubscription $subscription, string $jobId): void
    {
        $user = $subscription->user;

        Log::info("ExpireUserSubscriptionsJob: Expiring subscription", [
            'job_id' => $jobId,
            'subscription_id' => $subscription->id,
            'user_id' => $user->id,
            'user_email' => $user->email,
            'plan_name' => $subscription->subscriptionPlan->name,
            'expired_at' => $subscription->expires_at->toISOString(),
        ]);

        // Deactivate the subscription
        $subscription->update([
            'is_active' => false,
            'deactivated_at' => now(),
        ]);

        // Update user status
        $user->update([
            'expired' => true,
            'server_pool_id' => null, // Remove from server pool
        ]);

        // Dispatch event for cleanup (XUI clients deletion, etc.)
        UserSubscriptionExpired::dispatch($user);

        Log::info("ExpireUserSubscriptionsJob: Successfully expired subscription", [
            'job_id' => $jobId,
            'subscription_id' => $subscription->id,
            'user_id' => $user->id,
            'user_email' => $user->email,
        ]);
    }

    /**
     * Handle a job failure.
     */
    public function failed(\Throwable $exception): void
    {
        Log::error("ExpireUserSubscriptionsJob: Job failed after all retries", [
            'error' => $exception->getMessage(),
            'trace' => $exception->getTraceAsString(),
            'failed_at' => now()->toISOString(),
        ]);
    }
}
