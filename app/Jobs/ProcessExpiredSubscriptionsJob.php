<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class ProcessExpiredSubscriptionsJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public int $tries = 3;
    public int $timeout = 300; // 5 minutes

    /**
     * Execute the job.
     *
     * @deprecated Use ExpireUserSubscriptionsJob instead
     */
    public function handle(): void
    {
        Log::warning('ProcessExpiredSubscriptionsJob is deprecated, use ExpireUserSubscriptionsJob instead');

        // Dispatch the new job instead
        ExpireUserSubscriptionsJob::dispatch();

        Log::info('Dispatched ExpireUserSubscriptionsJob from deprecated ProcessExpiredSubscriptionsJob');
    }

    /**
     * Handle a job failure.
     */
    public function failed(\Throwable $exception): void
    {
        Log::error('Expired subscriptions processing job failed permanently', [
            'error' => $exception->getMessage(),
            'trace' => $exception->getTraceAsString(),
        ]);
    }
}
