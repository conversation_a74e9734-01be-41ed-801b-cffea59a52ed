<?php

namespace App\Events;

use App\Models\User;
use App\Models\Payment;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class ReferralRewardEarned implements ShouldQueue
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    /**
     * Create a new event instance.
     */
    public function __construct(
        public readonly User $referrer,
        public readonly User $referred,
        public readonly Payment $payment,
        public readonly float $rewardAmount
    ) {}

    /**
     * Get the tags that should be assigned to the job.
     */
    public function tags(): array
    {
        return [
            'referrer:' . $this->referrer->id,
            'referred:' . $this->referred->id,
            'payment:' . $this->payment->id,
            'referral-reward'
        ];
    }
}
