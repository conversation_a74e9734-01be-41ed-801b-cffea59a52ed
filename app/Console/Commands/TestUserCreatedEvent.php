<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\User;
use App\Models\SubscriptionPlan;
use App\Services\UserSubscriptionService;
use Illuminate\Support\Facades\Log;

class TestUserCreatedEvent extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'test:user-created-event';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test UserCreated event and listener';

    /**
     * Execute the console command.
     */
    public function handle(UserSubscriptionService $userSubscriptionService)
    {
        $this->info('Testing UserCreated event...');

        try {
            // Найдем демо план
            $demoPlan = SubscriptionPlan::where('is_demo', true)->first();
            if (!$demoPlan) {
                $this->error('Demo plan not found');
                return 1;
            }

            $this->info("Found demo plan: {$demoPlan->name}");

            // Создадим тестового пользователя
            $user = User::create([
                'name' => 'Test User ' . now()->format('H:i:s'),
                'email' => 'test' . time() . '@example.com',
                'password' => bcrypt('password'),
                'uuid' => \Str::uuid(),
            ]);

            $this->info("Created test user: {$user->email} (ID: {$user->id})");

            // Создадим подписку (это должно вызвать событие)
            Log::info("TestCommand: About to create subscription for user", [
                'user_id' => $user->id,
                'plan_id' => $demoPlan->id,
            ]);

            $subscription = $userSubscriptionService->createSubscription($user, $demoPlan);

            if ($subscription) {
                $this->info("Subscription created successfully: {$subscription->id}");
                Log::info("TestCommand: Subscription created", [
                    'subscription_id' => $subscription->id,
                ]);
            } else {
                $this->error("Failed to create subscription");
                return 1;
            }

            $this->info('Test completed. Check logs for event handling.');
            return 0;

        } catch (\Exception $e) {
            $this->error("Error: " . $e->getMessage());
            Log::error("TestCommand error", [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);
            return 1;
        }
    }
}
