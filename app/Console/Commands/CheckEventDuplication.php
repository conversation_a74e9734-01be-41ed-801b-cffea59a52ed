<?php

namespace App\Console\Commands;

use App\Events\UserCreated;
use App\Events\PaymentReceived;
use App\Events\UserSubscriptionRenewed;
use App\Events\UserSubscriptionExpired;
use App\Events\ReferralRewardEarned;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Event;

class CheckEventDuplication extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'check:event-duplication';

    /**
     * The console command description.
     */
    protected $description = 'Check for event listener duplication';

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $this->info('Checking for event listener duplication...');

        $events = [
            UserCreated::class,
            PaymentReceived::class,
            UserSubscriptionRenewed::class,
            UserSubscriptionExpired::class,
            ReferralRewardEarned::class,
        ];

        $hasDuplicates = false;

        foreach ($events as $eventClass) {
            $listeners = Event::getListeners($eventClass);
            $this->info("\n{$eventClass}:");
            $this->info("  Total listeners: " . count($listeners));

            // Count unique listeners
            $uniqueListeners = [];
            foreach ($listeners as $listener) {
                $listenerKey = $this->getListenerKey($listener);
                if (!isset($uniqueListeners[$listenerKey])) {
                    $uniqueListeners[$listenerKey] = 0;
                }
                $uniqueListeners[$listenerKey]++;
            }

            foreach ($uniqueListeners as $listenerKey => $count) {
                if ($count > 1) {
                    $this->error("  ⚠️  {$listenerKey} registered {$count} times");
                    $hasDuplicates = true;
                } else {
                    $this->info("  ✅ {$listenerKey}");
                }
            }
        }

        if ($hasDuplicates) {
            $this->error("\n❌ Found duplicate listeners!");
            return 1;
        } else {
            $this->info("\n✅ No duplicate listeners found!");
            return 0;
        }
    }

    /**
     * Get a unique key for a listener.
     */
    private function getListenerKey($listener): string
    {
        if (is_string($listener)) {
            return $listener;
        }
        
        if (is_array($listener) && count($listener) >= 2) {
            $class = is_object($listener[0]) ? get_class($listener[0]) : $listener[0];
            $method = $listener[1] ?? 'handle';
            return "{$class}@{$method}";
        }
        
        if (is_callable($listener)) {
            return 'Closure_' . spl_object_hash($listener);
        }
        
        return 'Unknown_' . serialize($listener);
    }
}
