<?php

namespace App\Console\Commands;

use App\Events\UserCreated;
use App\Events\PaymentReceived;
use App\Models\User;
use App\Models\Payment;
use App\Models\Order;
use App\Models\SubscriptionPlan;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Event;
use Illuminate\Support\Facades\Log;

class TestEventListeners extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'test:event-listeners';

    /**
     * The console command description.
     */
    protected $description = 'Test that event listeners are not duplicated';

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $this->info('Testing event listeners registration...');

        // Test UserCreated event listeners
        $this->testUserCreatedListeners();
        
        // Test PaymentReceived event listeners
        $this->testPaymentReceivedListeners();

        $this->info('Event listeners test completed. Check logs for details.');
        return 0;
    }

    private function testUserCreatedListeners(): void
    {
        $this->info('Testing UserCreated event listeners...');
        
        $listeners = Event::getListeners(UserCreated::class);
        $this->info('UserCreated listeners count: ' . count($listeners));
        
        foreach ($listeners as $index => $listener) {
            if (is_array($listener) && isset($listener[0])) {
                $listenerClass = is_object($listener[0]) ? get_class($listener[0]) : $listener[0];
                $this->info("Listener {$index}: {$listenerClass}");
            } else {
                $this->info("Listener {$index}: " . (is_callable($listener) ? 'Callable' : 'Unknown'));
            }
        }

        // Test actual event dispatch
        $user = User::factory()->make(['id' => 999, 'email' => '<EMAIL>']);
        
        Log::info('=== TESTING UserCreated EVENT DISPATCH ===');
        UserCreated::dispatch($user);
        Log::info('=== END UserCreated EVENT DISPATCH TEST ===');
    }

    private function testPaymentReceivedListeners(): void
    {
        $this->info('Testing PaymentReceived event listeners...');
        
        $listeners = Event::getListeners(PaymentReceived::class);
        $this->info('PaymentReceived listeners count: ' . count($listeners));
        
        foreach ($listeners as $index => $listener) {
            if (is_array($listener) && isset($listener[0])) {
                $listenerClass = is_object($listener[0]) ? get_class($listener[0]) : $listener[0];
                $this->info("Listener {$index}: {$listenerClass}");
            } else {
                $this->info("Listener {$index}: " . (is_callable($listener) ? 'Callable' : 'Unknown'));
            }
        }

        // Test actual event dispatch
        $user = User::factory()->make(['id' => 999]);
        $plan = SubscriptionPlan::factory()->make(['id' => 1]);
        $order = Order::factory()->make(['id' => 999, 'user_id' => 999, 'subscription_plan_id' => 1]);
        $payment = Payment::factory()->make(['id' => 999, 'order_id' => 999]);
        
        // Set the order relationship manually for the test
        $order->setRelation('user', $user);
        $payment->setRelation('order', $order);
        
        Log::info('=== TESTING PaymentReceived EVENT DISPATCH ===');
        PaymentReceived::dispatch($payment, $order);
        Log::info('=== END PaymentReceived EVENT DISPATCH TEST ===');
    }
}
