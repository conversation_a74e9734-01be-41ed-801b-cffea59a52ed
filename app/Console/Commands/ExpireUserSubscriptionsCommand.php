<?php

namespace App\Console\Commands;

use App\Jobs\ExpireUserSubscriptionsJob;
use App\Models\UserSubscription;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class ExpireUserSubscriptionsCommand extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'subscriptions:expire
                            {--dry-run : Show what would be expired without actually expiring}
                            {--force : Force expiration even if there are many subscriptions}';

    /**
     * The console command description.
     */
    protected $description = 'Expire user subscriptions that have passed their expiration date';

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $this->info('🔍 Checking for expired subscriptions...');

        try {
            // Find expired subscriptions
            $expiredSubscriptions = UserSubscription::where('is_active', true)
                ->where('expires_at', '<=', now())
                ->with(['user', 'subscriptionPlan'])
                ->get();

            $count = $expiredSubscriptions->count();

            if ($count === 0) {
                $this->info('✅ No expired subscriptions found.');
                return self::SUCCESS;
            }

            $this->info("📊 Found {$count} expired subscription(s):");

            // Show details of expired subscriptions
            // $this->table(
            //     ['ID', 'User', 'Email', 'Plan', 'Expired At', 'Days Overdue'],
            //     $expiredSubscriptions->map(function ($subscription) {
            //         $daysOverdue = now()->diffInDays($subscription->expires_at);
            //         return [
            //             $subscription->id,
            //             $subscription->user->name,
            //             $subscription->user->email,
            //             $subscription->subscriptionPlan->name,
            //             $subscription->expires_at->format('Y-m-d H:i:s'),
            //             $daysOverdue . ' days',
            //         ];
            //     })->toArray()
            // );

            // Dry run mode
            if ($this->option('dry-run')) {
                $this->warn('🔍 DRY RUN MODE: No subscriptions will be actually expired.');
                $this->info('💡 Remove --dry-run flag to actually expire these subscriptions.');
                return self::SUCCESS;
            }

            // Safety check for large numbers
            if ($count > 50 && !$this->option('force')) {
                $this->error("⚠️  Found {$count} expired subscriptions, which is quite a lot!");
                $this->error('This might indicate a problem or that the command hasn\'t been run for a while.');
                $this->info('💡 Use --force flag if you\'re sure you want to expire all these subscriptions.');
                $this->info('💡 Use --dry-run flag to see what would be expired without actually doing it.');
                return self::FAILURE;
            }

            // Confirm action
            if (!$this->option('force')) {
                if (!$this->confirm("Are you sure you want to expire {$count} subscription(s)?")) {
                    $this->info('❌ Operation cancelled.');
                    return self::SUCCESS;
                }
            }

            // Dispatch the job
            $this->info('🚀 Dispatching expiration job...');

            ExpireUserSubscriptionsJob::dispatch();

            $this->info('✅ Expiration job has been dispatched successfully!');
            $this->info('📝 Check the logs for detailed processing information.');
            $this->info('⏱️  The job will be processed by the queue worker.');

            // Log the command execution
            Log::info('ExpireUserSubscriptionsCommand: Command executed', [
                'expired_count' => $count,
                'dry_run' => $this->option('dry-run'),
                'force' => $this->option('force'),
                'executed_by' => 'console_command',
                'executed_at' => now()->toISOString(),
            ]);

            return self::SUCCESS;

        } catch (\Exception $e) {
            $this->error('❌ Error occurred while processing expired subscriptions:');
            $this->error($e->getMessage());

            Log::error('ExpireUserSubscriptionsCommand: Command failed', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return self::FAILURE;
        }
    }

    /**
     * Get the console command arguments.
     */
    protected function getArguments(): array
    {
        return [];
    }

    /**
     * Get the console command options.
     */
    protected function getOptions(): array
    {
        return [
            ['dry-run', null, null, 'Show what would be expired without actually expiring'],
            ['force', null, null, 'Force expiration even if there are many subscriptions'],
        ];
    }
}
