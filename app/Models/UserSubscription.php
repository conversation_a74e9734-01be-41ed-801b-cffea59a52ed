<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;
use Carbon\Carbon;

class UserSubscription extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'user_id',
        'subscription_plan_id',
        'server_pool_id',
        'started_at',
        'expires_at',
        'traffic_limit_gb',
        'traffic_used_gb',
        'is_active',
    ];

    protected function casts(): array
    {
        return [
            'started_at' => 'datetime',
            'expires_at' => 'datetime',
            'traffic_limit_gb' => 'integer',
            'traffic_used_gb' => 'integer',
            'is_active' => 'boolean',
        ];
    }

    /**
     * Get the user that owns this subscription.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the subscription plan.
     */
    public function subscriptionPlan(): BelongsTo
    {
        return $this->belongsTo(SubscriptionPlan::class);
    }

    /**
     * Get the server pool.
     */
    public function serverPool(): BelongsTo
    {
        return $this->belongsTo(ServerPool::class);
    }

    /**
     * Get only active subscriptions.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Get expired subscriptions.
     */
    public function scopeExpired($query)
    {
        return $query->where('expires_at', '<', now());
    }

    /**
     * Get subscriptions expiring soon.
     */
    public function scopeExpiringSoon($query, int $hours = 24)
    {
        return $query->where('expires_at', '<=', now()->addHours($hours))
                    ->where('expires_at', '>', now());
    }

    /**
     * Check if subscription is expired.
     */
    public function isExpired(): bool
    {
        return $this->expires_at && $this->expires_at->isPast();
    }

    /**
     * Check if subscription is active and not expired.
     */
    public function isActiveAndValid(): bool
    {
        return $this->is_active && !$this->isExpired();
    }

    /**
     * Get remaining days.
     */
    public function getRemainingDays(): int
    {
        if (!$this->expires_at || $this->isExpired()) {
            return 0;
        }

        return (int) now()->diffInDays($this->expires_at, false);
    }

    /**
     * Get remaining traffic in GB.
     */
    public function getRemainingTrafficGb(): int
    {
        if (!$this->traffic_limit_gb) {
            return PHP_INT_MAX; // Unlimited
        }

        return max(0, $this->traffic_limit_gb - $this->traffic_used_gb);
    }

    /**
     * Get traffic usage percentage.
     */
    public function getTrafficUsagePercentage(): float
    {
        if (!$this->traffic_limit_gb) {
            return 0; // Unlimited
        }

        return round(($this->traffic_used_gb / $this->traffic_limit_gb) * 100, 2);
    }

    /**
     * Check if traffic limit is exceeded.
     */
    public function isTrafficExceeded(): bool
    {
        if (!$this->traffic_limit_gb) {
            return false; // Unlimited
        }

        return $this->traffic_used_gb >= $this->traffic_limit_gb;
    }

    /**
     * Add traffic usage.
     */
    public function addTrafficUsage(int $bytes): void
    {
        $this->update(['traffic_used_gb', $bytes]);
    }

    /**
     * Reset traffic usage.
     */
    public function resetTrafficUsage(): void
    {
        $this->update(['traffic_used_gb' => 0]);
    }

    /**
     * Extend subscription.
     */
    public function extend(Carbon $newExpiryDate): void
    {
        $this->update([
            'expires_at' => $newExpiryDate,
            'is_active' => true,
        ]);
    }

    /**
     * Deactivate subscription.
     */
    public function deactivate(): void
    {
        $this->update(['is_active' => false]);
    }

    public function isRenewalRequired(): bool
    {
        return $this->isExpired() || $this->getRemainingDays() <= Setting::get('renewal_threshold_days');
    }

}
