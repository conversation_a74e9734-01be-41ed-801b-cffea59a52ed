<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Illuminate\Support\Str;

class User extends Authenticatable
{
    /** @use HasFactory<\Database\Factories\UserFactory> */
    use HasFactory, Notifiable, SoftDeletes;

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'uuid',
        'old_client_id',
        'name',
        'email',
        'tg_id',
        'demo_until',
        'comment',
        'expiry_time',
        'expired',
        'disabled_at',
        'total_gb',
        'used_gb',
        'up_traffic',
        'down_traffic',
        'use_common_routing',
        'server_pool_id',
        'subscription_plan_id',
        'source',
        'additional_data',
        'traffic_reset_at',
        'password',
        'last_online_at',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var list<string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
            'demo_until' => 'datetime',
            'expiry_time' => 'datetime',
            'expired' => 'boolean',
            'disabled_at' => 'datetime',
            'total_gb' => 'integer',
            'used_gb' => 'integer',
            'up_traffic' => 'integer',
            'down_traffic' => 'integer',
            'use_common_routing' => 'boolean',
            'additional_data' => 'array',
            'traffic_reset_at' => 'datetime',
            'password' => 'hashed',
            'last_online_at' => 'datetime',
        ];
    }

    /**
     * Boot the model.
     */
    protected static function boot(): void
    {
        parent::boot();

        static::creating(function ($user) {
            if (empty($user->uuid)) {
                $user->uuid = Str::uuid();
            }
        });
    }

    /**
     * Get the server pool assigned to this user.
     */
    public function serverPool(): BelongsTo
    {
        return $this->belongsTo(ServerPool::class);
    }

    /**
     * Get the subscription plan assigned to this user.
     */
    public function subscriptionPlan(): BelongsTo
    {
        return $this->belongsTo(SubscriptionPlan::class);
    }

    /**
     * Get the user subscriptions.
     */
    public function userSubscriptions(): HasMany
    {
        return $this->hasMany(UserSubscription::class);
    }

    /**
     * Get the current active subscription.
     */
    public function currentActiveSubscription(): ?UserSubscription
    {
        return $this->userSubscriptions()
            ->where('is_active', true)
            ->where(function ($query) {
                $query->whereNull('expires_at')
                      ->orWhere('expires_at', '>', now());
            })
            ->first();
    }

    /**
     * Get the current subscription with any status.
     */
    public function getCurrentSubscription(): ?UserSubscription
    {
        return $this->userSubscriptions()->first();
    }

    /**
     * Get the current subscription relationship.
     */
    public function currentSubscription(): HasOne
    {
        return $this->hasOne(UserSubscription::class)->latest();
    }

    /**
     * Check if user has an active subscription.
     */
    public function hasActiveSubscription(): bool
    {
        return $this->currentActiveSubscription() !== null;
    }

    /**
     * Get the client ID to use for VLESS keys.
     * Returns old_client_id if set, otherwise returns uuid.
     */
    public function getClientId(): string
    {
        return $this->old_client_id ?: $this->uuid;
    }

    /**
     * Find user by UUID.
     */
    public static function findByUuid(string $uuid): ?self
    {
        return self::where('uuid', $uuid)->first();
    }

    /**
     * Generate client email for XUI (user_email + "_" + inbound_id).
     */
    public function generateClientEmail(int $inboundId): string
    {
        return $this->email . '_' . $inboundId;
    }

    /**
     * Extract user email from client email.
     */
    public static function extractUserEmailFromClientEmail(string $clientEmail): string
    {
        preg_match('/^(.*?)(?:_\d+)?$/iu', $clientEmail, $matches);
        return $matches[1] ?? '';
    }

    /**
     * Get the ratings for the user.
     */
    public function ratings(): HasMany
    {
        return $this->hasMany(UserRating::class);
    }

    /**
     * Get the subscription extensions for the user.
     */
    public function subscriptionExtensions(): HasMany
    {
        return $this->hasMany(SubscriptionExtension::class);
    }



    /**
     * Find or create user by tg_id or email.
     */
    public static function findOrCreateByClientIdOrTgIdOrEmail(?string $clientId,  ?string $tgId, ?string $email): ?self
    {
        if (!$clientId && !$tgId && !$email) {
            return null;
        }

        $query = static::query();

        if ($clientId) {
            $query->where('uuid', $clientId);
        } elseif ($tgId) {
            $query->where('tg_id', $tgId);
        } elseif ($email) {
            $query->where('email', $email);
        }

        if (preg_match('/client(\d+)-?/', $email, $matches)) {
            $tgId = $matches[1];
            $query->where('tg_id', $tgId)
                    ->orWhere('email', "client{$tgId}");
        }

        $user = $query->first();

        if (!$user && ($tgId || $email)) {
            $user = static::create([
                'uuid' => Str::uuid(),
                'tg_id' => $tgId,
                'email' => $email,
                'name' => $email ?? "User_{$tgId}",
            ]);
        }

        return $user;
    }

    /**
     * Check if user is online.
     */
    public function isOnline(): bool
    {
        return $this->last_online_at && $this->last_online_at->diffInMinutes(now()) < 2;
    }
}
