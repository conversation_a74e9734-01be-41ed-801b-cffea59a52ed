<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Carbon\Carbon;

class SubscriptionPlan extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'name',
        'description',
        'duration_amount',
        'duration_unit',
        'traffic_gb',
        'server_count',
        'price_in_cents',
        'price_formatted',
        'discount_percent',
        'is_demo',
        'is_active',
        'is_archived',
    ];

    protected function casts(): array
    {
        return [
            'duration_amount' => 'integer',
            'traffic_gb' => 'integer',
            'server_count' => 'integer',
            'price_in_cents' => 'integer',
            'price_formatted' => 'string',
            'discount_percent' => 'decimal:2',
            'is_demo' => 'boolean',
            'is_active' => 'boolean',
            'is_archived' => 'boolean',
        ];
    }

    /**
     * Get the users with this subscription plan.
     */
    public function users(): HasMany
    {
        return $this->hasMany(User::class);
    }

    /**
     * Get the user subscriptions for this plan.
     */
    public function userSubscriptions(): HasMany
    {
        return $this->hasMany(UserSubscription::class);
    }

    /**
     * Get only active plans.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true)->where('is_archived', false);
    }

    /**
     * Get only demo plans.
     */
    public function scopeDemo($query)
    {
        return $query->where('is_demo', true);
    }

    /**
     * Get only non-demo plans.
     */
    public function scopeNonDemo($query)
    {
        return $query->where('is_demo', false);
    }

    /**
     * Calculate final price with discount.
     */
    public function getFinalPriceInCents(): int
    {
        if ($this->discount_percent > 0) {
            return (int) round($this->price_in_cents * (1 - $this->discount_percent / 100));
        }

        return $this->price_in_cents;
    }

    /**
     * Get price.
     */
    public function getPrice(): float
    {
        return $this->price_in_cents / 100;
    }

    /**
     * Get final price.
     */
    public function getFinalPrice(): float
    {
        return $this->getFinalPriceInCents() / 100;
    }

    /**
     * Calculate expiry date from start date.
     */
    public function calculateExpiryDate(?Carbon $startDate = null): Carbon
    {
        $startDate = $startDate ?? now();

        return match($this->duration_unit) {
            'day' => $startDate->copy()->addDays($this->duration_amount),
            'month' => $startDate->copy()->addMonths($this->duration_amount),
            'year' => $startDate->copy()->addYears($this->duration_amount),
            default => $startDate->copy()->addDays($this->duration_amount),
        };
    }

    /**
     * Get human readable duration.
     */
    public function getDurationLabel(): string
    {
        $unit = match($this->duration_unit) {
            'day' => $this->duration_amount === 1 ? 'день' : ($this->duration_amount < 5 ? 'дня' : 'дней'),
            'month' => $this->duration_amount === 1 ? 'месяц' : ($this->duration_amount < 5 ? 'месяца' : 'месяцев'),
            'year' => $this->duration_amount === 1 ? 'год' : ($this->duration_amount < 5 ? 'года' : 'лет'),
            default => 'дней',
        };

        return "{$this->duration_amount} {$unit}";
    }

    /**
     * Get traffic label.
     */
    public function getTrafficLabel(): string
    {
        return $this->traffic_gb ? "{$this->traffic_gb} ГБ" : 'Безлимит';
    }

    /**
     * Check if plan is free.
     */
    public function isFree(): bool
    {
        return $this->price_in_cents === 0;
    }
}
