# Система событий Laravel 12

## Обзор

Система событий была переписана в соответствии с рекомендациями Laravel 12, используя современные подходы и автоматическое обнаружение слушателей.

## Ключевые изменения

### 1. Автоматическое обнаружение событий

Включено автоматическое обнаружение слушателей в `EventServiceProvider`:

```php
public function shouldDiscoverEvents(): bool
{
    return true;
}

protected function discoverEventsWithin(): array
{
    return [
        app_path('Listeners'),
    ];
}
```

### 2. Обновленные события

Все события обновлены с современными трейтами и интерфейсами:

- `InteractsWithSockets` - для поддержки broadcasting
- `readonly` свойства для неизменяемости данных
- Методы `tags()` для лучшей организации в очередях
- `ShouldQueue` для асинхронных событий где необходимо

#### Пример события:

```php
class UserCreated
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public function __construct(
        public readonly User $user
    ) {}

    public function tags(): array
    {
        return ['user:' . $this->user->id, 'user-created'];
    }
}
```

### 3. Улучшенные слушатели

Все слушатели обновлены с:

- Интерфейсом `ShouldQueue` для асинхронной обработки
- Настройками retry (`$tries`, `$backoff`)
- Методами `failed()` для обработки ошибок
- Правильной типизацией методов `handle()` для автоматического обнаружения

#### Пример слушателя:

```php
class CreateXuiClientsListener implements ShouldQueue
{
    use InteractsWithQueue;

    public int $tries = 3;
    public int $backoff = 30;

    public function handle(UserCreated $event): void
    {
        // Логика обработки события
    }

    public function failed(UserCreated $event, \Throwable $exception): void
    {
        // Обработка ошибок
    }
}
```

## Структура событий

### События пользователей

- **UserCreated** - создание пользователя
  - Слушатель: `CreateXuiClientsListener`

- **UserSubscriptionRenewed** - продление подписки
  - Слушатель: `UpdateXuiClientsListener`

- **UserSubscriptionExpired** - истечение подписки
  - Слушатель: `DeleteXuiClientsListener`

### События платежей

- **PaymentReceived** - получение платежа
  - Слушатели: 
    - `ProcessSubscriptionActivationListener`
    - `UpdateLeadtehVariablesListener`

### События рефералов

- **ReferralRewardEarned** - начисление реферальной награды
  - Слушатель: `ProcessReferralRewardListener`

## Диспетчеризация событий

### Основные методы

```php
// Обычная диспетчеризация
UserCreated::dispatch($user);

// Условная диспетчеризация
UserCreated::dispatchIf($condition, $user);
UserCreated::dispatchUnless($condition, $user);

// Синхронная диспетчеризация
UserCreated::dispatchSync($user);
```

### Примеры использования

```php
// В сервисе создания пользователя
UserCreated::dispatchIf($subscription !== null, $user);

// В сервисе платежей
PaymentReceived::dispatch($payment, $order);

// В сервисе подписок
UserSubscriptionExpired::dispatch($user);
```

## Тестирование

### Тестирование событий

```php
Event::fake();

UserCreated::dispatch($user);

Event::assertDispatched(UserCreated::class, function ($event) use ($user) {
    return $event->user->id === $user->id;
});
```

### Проверка дублирования слушателей

Создан специальный тест `EventListenerDuplicationTest` для проверки того, что слушатели не дублируются.

## Команды для тестирования

### Тестирование слушателей

```bash
php artisan test:event-listeners
```

Эта команда показывает:
- Количество зарегистрированных слушателей для каждого события
- Список всех слушателей
- Тестирует фактическую диспетчеризацию событий

### Запуск тестов

```bash
php artisan test tests/Feature/EventsTest.php
php artisan test tests/Feature/EventListenerDuplicationTest.php
```

## Преимущества новой системы

1. **Автоматическое обнаружение** - Laravel автоматически находит и регистрирует слушатели
2. **Отсутствие дублирования** - каждый слушатель регистрируется только один раз
3. **Лучшая производительность** - асинхронная обработка через очереди
4. **Надежность** - retry логика и обработка ошибок
5. **Типобезопасность** - строгая типизация событий и слушателей
6. **Тегирование** - лучшая организация задач в очередях

## Миграция с старой системы

1. Удалены ручные регистрации в `$listen` массиве
2. Удалены подписчики событий (избегание дублирования)
3. Включено автоматическое обнаружение
4. Обновлены все события и слушатели
5. Добавлены тесты для проверки корректности

## Мониторинг и отладка

Для отладки событий можно использовать:

```php
// Получить список слушателей для события
$listeners = Event::getListeners(UserCreated::class);

// Проверить, есть ли слушатели
$hasListeners = Event::hasListeners(UserCreated::class);
```

Логи событий можно найти в стандартных логах Laravel, все слушатели логируют свою работу.
