# Система обработки истекших подписок

## Обзор

Новая система для автоматической обработки истекших подписок пользователей, включающая команду, джоб и события для корректного завершения подписок.

## Компоненты системы

### 1. ExpireUserSubscriptionsJob

Основной джоб для обработки истекших подписок.

**Функциональность:**
- Находит все активные подписки с истекшим сроком действия
- Деактивирует подписки (устанавливает `is_active = false`)
- Обновляет статус пользователей (`expired = true`)
- Удаляет пользователей из пула серверов
- Диспетчеризует событие `UserSubscriptionExpired` для каждого пользователя
- Подробное логирование всех операций

**Особенности:**
- Работает в транзакции для обеспечения целостности данных
- Обрабатывает ошибки индивидуально для каждой подписки
- Retry логика (3 попытки с интервалом 60 секунд)
- Подробная метрика производительности

### 2. ExpireUserSubscriptionsCommand

Консольная команда для запуска обработки истекших подписок.

**Использование:**
```bash
# Обычный запуск с подтверждением
php artisan subscriptions:expire

# Режим предварительного просмотра (без изменений)
php artisan subscriptions:expire --dry-run

# Принудительный запуск без подтверждения
php artisan subscriptions:expire --force
```

**Функции:**
- Показывает таблицу с деталями истекших подписок
- Режим dry-run для безопасного тестирования
- Защита от случайного массового истечения (лимит 50 подписок)
- Подтверждение действий пользователем
- Принудительный режим для автоматизации

### 3. Интеграция с событиями

При истечении подписки диспетчеризуется событие `UserSubscriptionExpired`, которое обрабатывается слушателем `DeleteXuiClientsListener` для:
- Удаления клиентов пользователя с XUI серверов
- Очистки конфигураций
- Логирования операций

## Автоматическое выполнение

Команда автоматически запускается каждый час через планировщик Laravel:

```php
Schedule::command('subscriptions:expire --force')->hourly()
    ->withoutOverlapping()
    ->runInBackground()
    ->onOneServer();
```

**Параметры планировщика:**
- `hourly()` - выполнение каждый час
- `withoutOverlapping()` - предотвращение одновременного выполнения
- `runInBackground()` - выполнение в фоне
- `onOneServer()` - выполнение только на одном сервере (для кластерных установок)

## Миграция со старой системы

Старый `ProcessExpiredSubscriptionsJob` помечен как deprecated и теперь просто диспетчеризует новый джоб:

```php
/**
 * @deprecated Use ExpireUserSubscriptionsJob instead
 */
public function handle(): void 
{
    Log::warning('ProcessExpiredSubscriptionsJob is deprecated, use ExpireUserSubscriptionsJob instead');
    ExpireUserSubscriptionsJob::dispatch();
}
```

## Тестирование

### Тестирование джоба

```bash
php artisan test tests/Feature/ExpireUserSubscriptionsJobTest.php
```

**Покрытие тестами:**
- Корректное истечение подписок
- Диспетчеризация событий
- Обработка случаев без истекших подписок
- Обработка некорректных данных

### Тестирование команды

```bash
php artisan test tests/Feature/ExpireUserSubscriptionsCommandTest.php
```

**Покрытие тестами:**
- Режим dry-run
- Подтверждение действий
- Защита от массового истечения
- Принудительный режим
- Отображение информации

## Мониторинг и логирование

### Логи джоба

```
ExpireUserSubscriptionsJob: Starting to process expired subscriptions
ExpireUserSubscriptionsJob: Found expired subscriptions (count: X)
ExpireUserSubscriptionsJob: Expiring subscription (subscription_id: X, user_id: Y)
ExpireUserSubscriptionsJob: Successfully expired subscription
ExpireUserSubscriptionsJob: Completed processing (processed: X, errors: Y, time: Z ms)
```

### Логи команды

```
ExpireUserSubscriptionsCommand: Command executed (expired_count: X, dry_run: false, force: true)
```

### Метрики

Джоб отслеживает:
- Количество найденных истекших подписок
- Количество успешно обработанных
- Количество ошибок
- Время выполнения в миллисекундах

## Безопасность

### Защитные механизмы

1. **Лимит безопасности**: Команда предупреждает при обнаружении >50 истекших подписок
2. **Подтверждение**: Требует подтверждения пользователя (кроме режима --force)
3. **Dry-run режим**: Позволяет просмотреть изменения без их применения
4. **Транзакции**: Все изменения в БД выполняются в транзакции
5. **Retry логика**: Автоматические повторы при временных ошибках

### Рекомендации

- Используйте `--dry-run` перед первым запуском
- Мониторьте логи на предмет ошибок
- Настройте алерты на большое количество истекших подписок
- Регулярно проверяйте работу планировщика

## Примеры использования

### Ручная проверка истекших подписок

```bash
# Посмотреть что будет истечено
php artisan subscriptions:expire --dry-run

# Истечь подписки с подтверждением
php artisan subscriptions:expire

# Принудительное истечение (для автоматизации)
php artisan subscriptions:expire --force
```

### Диспетчеризация джоба программно

```php
use App\Jobs\ExpireUserSubscriptionsJob;

// Диспетчеризация в очередь
ExpireUserSubscriptionsJob::dispatch();

// Синхронное выполнение
ExpireUserSubscriptionsJob::dispatchSync();
```

### Обработка событий

```php
use App\Events\UserSubscriptionExpired;

// Слушатель автоматически обрабатывает событие
// DeleteXuiClientsListener удаляет клиентов с серверов
```

## Troubleshooting

### Частые проблемы

1. **Джоб не выполняется**: Проверьте работу queue worker
2. **Много ошибок**: Проверьте подключение к XUI серверам
3. **Подписки не истекают**: Проверьте планировщик Laravel
4. **Дублирование обработки**: Убедитесь что используется `withoutOverlapping()`

### Отладка

```bash
# Проверить планировщик
php artisan schedule:list

# Запустить планировщик вручную
php artisan schedule:run

# Проверить очереди
php artisan queue:work --verbose

# Посмотреть логи
tail -f storage/logs/laravel.log
```
