<?php

require_once __DIR__ . '/vendor/autoload.php';

use App\Services\TbankAcquiringService;
use Illuminate\Foundation\Application;
use Illuminate\Foundation\Configuration\Exceptions;
use Illuminate\Foundation\Configuration\Middleware;

// Bootstrap Laravel
$app = Application::configure(basePath: __DIR__)
    ->withRouting(
        web: __DIR__.'/routes/web.php',
        api: __DIR__.'/routes/api.php',
        commands: __DIR__.'/routes/console.php',
        health: '/up',
    )
    ->withMiddleware(function (Middleware $middleware) {
        //
    })
    ->withExceptions(function (Exceptions $exceptions) {
        //
    })->create();

$app->make(\Illuminate\Contracts\Console\Kernel::class)->bootstrap();

try {
    // Test webhook data from the latest log
    $webhookData = [
        "TerminalKey" => "1726392591291DEMO",
        "OrderId" => "SV01-T5AOEMQ",
        "Success" => true,
        "Status" => "CONFIRMED",
        "PaymentId" => **********,
        "ErrorCode" => "0",
        "Amount" => 79900,
        "CardId" => *********,
        "Pan" => "430000******0777",
        "ExpDate" => "1230",
        "Token" => "6912285f46f424f5d9c8bbeaf1dfc82147f4e2176e384f3c330f7b199f0c669c"
    ];

    echo "Testing T-Bank webhook signature verification...\n";
    echo "Original webhook data:\n";
    print_r($webhookData);

    // Create TbankAcquiringService instance
    $tbankService = app(TbankAcquiringService::class);

    // Test signature verification
    $isValid = $tbankService->verifyWebhookSignature($webhookData);

    echo "\nSignature verification result: " . ($isValid ? "VALID" : "INVALID") . "\n";

    // Manual test according to T-Bank documentation
    echo "\nTesting manual token generation according to T-Bank docs...\n";

    // Step 1: Get only root-level scalar values (exclude nested objects/arrays)
    $rootLevelData = [];
    foreach ($webhookData as $key => $value) {
        if ($key !== 'Token' && !is_array($value) && !is_object($value)) {
            $rootLevelData[$key] = $value;
        }
    }

    echo "\nRoot-level fields: " . implode(', ', array_keys($rootLevelData)) . "\n";

    // Step 2: Add Password
    $rootLevelData['Password'] = config('services.tbank.secret_key');

    // Step 3: Convert to strings
    foreach ($rootLevelData as $key => $value) {
        if (is_bool($value)) {
            $rootLevelData[$key] = $value ? 'true' : 'false';
        } else {
            $rootLevelData[$key] = (string) $value;
        }
    }

    // Step 4: Sort alphabetically by key
    ksort($rootLevelData);

    echo "Sorted fields: " . implode(', ', array_keys($rootLevelData)) . "\n";
    echo "Values: " . implode(', ', array_values($rootLevelData)) . "\n";

    // Step 5: Concatenate values
    $tokenString = implode('', array_values($rootLevelData));
    echo "\nToken string: " . $tokenString . "\n";

    // Step 6: SHA-256 hash
    $calculatedToken = hash('sha256', $tokenString);
    echo "Calculated token: " . $calculatedToken . "\n";
    echo "Received token:   " . $webhookData['Token'] . "\n";
    echo "Match: " . ($calculatedToken === $webhookData['Token'] ? "YES" : "NO") . "\n";

    if (!$isValid && $calculatedToken !== $webhookData['Token']) {
        echo "\nTrying alternative approaches...\n";

        // Try without Success and ErrorCode
        $altData = $rootLevelData;
        unset($altData['Success'], $altData['ErrorCode']);
        ksort($altData);
        $altTokenString = implode('', array_values($altData));
        $altToken = hash('sha256', $altTokenString);
        echo "Without Success/ErrorCode: " . ($altToken === $webhookData['Token'] ? "MATCH" : "NO MATCH") . "\n";

        // Try with different boolean format
        $altData2 = $rootLevelData;
        $altData2['Success'] = $webhookData['Success'] ? '1' : '0';
        ksort($altData2);
        $altTokenString2 = implode('', array_values($altData2));
        $altToken2 = hash('sha256', $altTokenString2);
        echo "Success as 1/0: " . ($altToken2 === $webhookData['Token'] ? "MATCH" : "NO MATCH") . "\n";
    }

    echo "\nTest completed!\n";

} catch (\Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
    echo "File: " . $e->getFile() . ":" . $e->getLine() . "\n";
    echo "Trace:\n" . $e->getTraceAsString() . "\n";
    exit(1);
}
