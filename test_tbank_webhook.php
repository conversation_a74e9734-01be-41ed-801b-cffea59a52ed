<?php

require_once __DIR__ . '/vendor/autoload.php';

use App\Services\TbankAcquiringService;
use Illuminate\Foundation\Application;
use Illuminate\Foundation\Configuration\Exceptions;
use Illuminate\Foundation\Configuration\Middleware;

// Bootstrap Laravel
$app = Application::configure(basePath: __DIR__)
    ->withRouting(
        web: __DIR__.'/routes/web.php',
        api: __DIR__.'/routes/api.php',
        commands: __DIR__.'/routes/console.php',
        health: '/up',
    )
    ->withMiddleware(function (Middleware $middleware) {
        //
    })
    ->withExceptions(function (Exceptions $exceptions) {
        //
    })->create();

$app->make(\Illuminate\Contracts\Console\Kernel::class)->bootstrap();

try {
    // Test webhook data from the log
    $webhookData = [
        "TerminalKey" => "1726392591291DEMO",
        "OrderId" => "SV01-YSIOISU",
        "Success" => true,
        "Status" => "AUTHORIZED",
        "PaymentId" => **********,
        "ErrorCode" => "0",
        "Amount" => 39900,
        "CardId" => *********,
        "Pan" => "430000******0777",
        "ExpDate" => "1230",
        "Token" => "89452bf8ee6fbe84eff52585211a82092a98506c22e50e7d057f712c04b8c102"
    ];

    echo "Testing T-Bank webhook signature verification...\n";
    echo "Original webhook data:\n";
    print_r($webhookData);

    // Create TbankAcquiringService instance
    $tbankService = app(TbankAcquiringService::class);

    // Test signature verification
    $isValid = $tbankService->verifyWebhookSignature($webhookData);

    echo "\nSignature verification result: " . ($isValid ? "VALID" : "INVALID") . "\n";

    if (!$isValid) {
        echo "\nTesting manual token generation...\n";
        
        // Manually test token generation with different field combinations
        $testData = $webhookData;
        unset($testData['Token']);
        
        echo "\nTest 1: All fields except Token\n";
        $testData1 = $testData;
        echo "Fields: " . implode(', ', array_keys($testData1)) . "\n";
        
        echo "\nTest 2: Excluding Success, ErrorCode, CardId, Pan, ExpDate\n";
        $testData2 = $testData;
        unset($testData2['Success'], $testData2['ErrorCode'], $testData2['CardId'], $testData2['Pan'], $testData2['ExpDate']);
        echo "Fields: " . implode(', ', array_keys($testData2)) . "\n";
        
        echo "\nTest 3: Only core fields (TerminalKey, OrderId, Status, PaymentId, Amount)\n";
        $testData3 = [
            'TerminalKey' => $webhookData['TerminalKey'],
            'OrderId' => $webhookData['OrderId'],
            'Status' => $webhookData['Status'],
            'PaymentId' => $webhookData['PaymentId'],
            'Amount' => $webhookData['Amount'],
        ];
        echo "Fields: " . implode(', ', array_keys($testData3)) . "\n";
    }

    echo "\nTest completed!\n";

} catch (\Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
    echo "File: " . $e->getFile() . ":" . $e->getLine() . "\n";
    echo "Trace:\n" . $e->getTraceAsString() . "\n";
    exit(1);
}
