<!DOCTYPE html>
<html lang="ru" x-data="{ darkMode: true }" :class="{ 'dark': darkMode }">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title>{{ $data['is_renewal'] ? 'Продление подписки' : 'Покупка подписки' }} - {{ $data['user']['email'] }}</title>

    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    colors: {
                        primary: {
                            50: '#eff6ff',
                            500: '#3b82f6',
                            600: '#2563eb',
                            700: '#1d4ed8',
                        }
                    }
                }
            }
        }
    </script>

    <!-- Alpine.js -->
    <script defer src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js"></script>

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <style>
        [x-cloak] { display: none !important; }

        .plan-card {
            transition: all 0.3s ease;
        }

        .plan-card:hover {
            transform: translateY(-2px);
        }

        .plan-card.selected {
            border-color: #3b82f6;
            box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2);
        }

        .loading-spinner {
            border: 2px solid #f3f3f3;
            border-top: 2px solid #3b82f6;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>

<body class="dark:bg-gray-900 bg-gray-50 min-h-screen">
    <div class="container mx-auto px-4 py-8 max-w-4xl">
        <!-- Header -->
        <div class="text-center mb-8">
            <h1 class="text-3xl font-bold dark:text-white text-gray-900 mb-2">
                @if($data['is_renewal'])
                    <i class="fas fa-sync-alt mr-2"></i>Продление подписки
                @else
                    <i class="fas fa-shopping-cart mr-2"></i>Покупка подписки
                @endif
            </h1>
            <p class="dark:text-gray-300 text-gray-600">
                Пользователь: <span class="font-semibold">{{ $data['user']['email'] }}</span>
            </p>

            @if($data['current_subscription'])
                <div class="mt-4 p-4 rounded-lg {{ $data['current_subscription']['is_demo'] ? 'bg-yellow-100 dark:bg-yellow-900' : 'bg-blue-100 dark:bg-blue-900' }}">
                    <p class="text-sm {{ $data['current_subscription']['is_demo'] ? 'text-yellow-800 dark:text-yellow-200' : 'text-blue-800 dark:text-blue-200' }}">
                        @if($data['current_subscription']['is_demo'])
                            <i class="fas fa-clock mr-1"></i>Текущий план: {{ $data['current_subscription']['plan_name'] }} (Демо)
                        @else
                            <i class="fas fa-star mr-1"></i>Текущий план: {{ $data['current_subscription']['plan_name'] }}
                        @endif

                        @if($data['current_subscription']['expires_at'])
                            <br>Истекает: {{ \Carbon\Carbon::parse($data['current_subscription']['expires_at'])->format('d.m.Y H:i') }}
                        @endif
                    </p>
                </div>
            @endif
        </div>

        <!-- Subscription Plans -->
        <div x-data="subscriptionRenewal()" x-cloak>
            <form @submit.prevent="processPayment()">
                <div class="space-y-4 mb-8">
                    <h2 class="text-xl font-semibold dark:text-white text-gray-900 mb-4">
                        <i class="fas fa-list mr-2"></i>Выберите тарифный план:
                    </h2>

                    @foreach($data['subscription_plans'] as $key => $plan)
                        <div class="plan-card border-2 rounded-lg p-6 cursor-pointer dark:bg-gray-800 bg-white"
                             :class="{ 'selected': selectedPlanId === {{ $plan['id'] }} }"
                             @click="selectedPlanId = {{ $plan['id'] }}">

                            <div class="flex items-start justify-between">
                                <div class="flex items-start space-x-4">
                                    <input type="radio"
                                           name="plan_id"
                                           value="{{ $plan['id'] }}"
                                           x-model="selectedPlanId"
                                           class="mt-1 text-primary-600 focus:ring-primary-500">

                                    <div class="flex-1">
                                        <div class="flex items-center space-x-2 mb-2">
                                            <h3 class="text-lg font-semibold dark:text-white text-gray-900">
                                                {{ $plan['name'] }}
                                            </h3>

                                            @if($plan['is_demo'])
                                                <span class="px-2 py-1 text-xs font-medium bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200 rounded-full">
                                                    ДЕМО
                                                </span>
                                            @endif

                                            @if($plan['discount_percent'] > 0)
                                                <span class="px-2 py-1 text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200 rounded-full">
                                                    -{{ $plan['discount_percent'] }}%
                                                </span>
                                            @endif
                                        </div>

                                        @if($plan['description'])
                                            <p class="dark:text-gray-300 text-gray-600 text-sm mb-3">
                                                {{ $plan['description'] }}
                                            </p>
                                        @endif

                                        <div class="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                                            <div class="flex items-center space-x-2">
                                                <i class="fas fa-clock text-primary-500"></i>
                                                <span class="dark:text-gray-300 text-gray-600">
                                                    {{ $plan['duration_amount'] }}
                                                    @if($plan['duration_unit'] === 'days')
                                                        {{ trans_choice('дн.|дн.|дн.', $plan['duration_amount']) }}
                                                    @elseif($plan['duration_unit'] === 'months')
                                                        {{ trans_choice('мес.|мес.|мес.', $plan['duration_amount']) }}
                                                    @elseif($plan['duration_unit'] === 'years')
                                                        {{ trans_choice('год|года|лет', $plan['duration_amount']) }}
                                                    @endif
                                                </span>
                                            </div>

                                            <div class="flex items-center space-x-2">
                                                <i class="fas fa-database text-primary-500"></i>
                                                <span class="dark:text-gray-300 text-gray-600">
                                                    @if($plan['traffic_gb'])
                                                        {{ $plan['traffic_gb'] }} ГБ
                                                    @else
                                                        Безлимит
                                                    @endif
                                                </span>
                                            </div>

                                            <div class="flex items-center space-x-2">
                                                <i class="fas fa-server text-primary-500"></i>
                                                <span class="dark:text-gray-300 text-gray-600">
                                                    Все серверы
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="text-right">
                                    @if($plan['discount_percent'] > 0)
                                        <div class="text-sm dark:text-gray-400 text-gray-500 line-through">
                                            {{ number_format($plan['price_in_cents'] / 100, 0, '.', ' ') }} ₽
                                        </div>
                                    @endif

                                    <div class="text-2xl font-bold dark:text-white text-gray-900">
                                        {{ $plan['final_price_formatted'] }}
                                    </div>
                                </div>
                            </div>
                        </div>
                    @endforeach
                </div>

                <!-- Payment Method -->
                <div class="mb-8">
                    <h3 class="text-lg font-semibold dark:text-white text-gray-900 mb-4">
                        <i class="fas fa-credit-card mr-2"></i>Способ оплаты:
                    </h3>

                    <div class="space-y-3">
                        @foreach($data['payment_methods'] as $method => $label)
                            <label class="flex items-center space-x-3 p-4 border-2 rounded-lg cursor-pointer dark:bg-gray-800 bg-white hover:border-primary-500 transition-colors">
                                <input type="radio"
                                       name="payment_method"
                                       value="{{ $method }}"
                                       x-model="paymentMethod"
                                       class="text-primary-600 focus:ring-primary-500">

                                <div class="flex items-center space-x-3">
                                    @if($method === 'tbank')
                                        <i class="fab fa-cc-visa text-2xl text-blue-600"></i>
                                    @endif
                                    <span class="dark:text-white text-gray-900 font-medium">{{ $label }}</span>
                                </div>
                            </label>
                        @endforeach
                    </div>
                </div>

                <!-- Submit Button -->
                <div class="text-center">
                    <button type="submit"
                            :disabled="!selectedPlanId || !paymentMethod || processing"
                            class="w-full md:w-auto px-8 py-4 bg-primary-600 hover:bg-primary-700 disabled:bg-gray-400 disabled:cursor-not-allowed text-white font-semibold rounded-lg transition-colors flex items-center justify-center space-x-2">

                        <template x-if="processing">
                            <div class="loading-spinner"></div>
                        </template>

                        <template x-if="!processing">
                            <i class="fas fa-arrow-right"></i>
                        </template>

                        <span x-text="processing ? 'Обработка...' : 'Перейти к оплате'"></span>
                    </button>

                    <p class="mt-4 text-sm dark:text-gray-400 text-gray-500">
                        <i class="fas fa-shield-alt mr-1"></i>
                        Безопасная оплата через T-Bank
                    </p>
                </div>
            </form>

            <!-- Error Message -->
            <div x-show="errorMessage"
                 x-transition
                 class="mt-6 p-4 bg-red-100 dark:bg-red-900 border border-red-400 dark:border-red-600 text-red-700 dark:text-red-200 rounded-lg">
                <div class="flex items-center space-x-2">
                    <i class="fas fa-exclamation-triangle"></i>
                    <span x-text="errorMessage"></span>
                </div>
            </div>
        </div>
    </div>

    <script>
        function subscriptionRenewal() {
            return {
                selectedPlanId: {{ $data['default_plan_id'] }},
                paymentMethod: 'tbank',
                processing: false,
                errorMessage: '',

                async processPayment() {
                    if (!this.selectedPlanId || !this.paymentMethod) {
                        this.errorMessage = 'Пожалуйста, выберите план и способ оплаты';
                        return;
                    }

                    this.processing = true;
                    this.errorMessage = '';

                    try {
                        const response = await fetch(`{{ config('app.url') }}/subs/{{ $data['user']['uuid'] }}/renew`, {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                                'Accept': 'application/json'
                            },
                            body: JSON.stringify({
                                plan_id: this.selectedPlanId,
                                payment_method: this.paymentMethod
                            })
                        });

                        const data = await response.json();

                        if (data.success && data.payment_url) {
                            // Redirect to payment page
                            window.location.href = data.payment_url;
                        } else {
                            this.errorMessage = data.message || 'Произошла ошибка при создании платежа';
                        }

                    } catch (error) {
                        console.error('Payment error:', error);
                        this.errorMessage = 'Произошла ошибка при обработке запроса';
                    } finally {
                        this.processing = false;
                    }
                }
            }
        }
    </script>
</body>
</html>
