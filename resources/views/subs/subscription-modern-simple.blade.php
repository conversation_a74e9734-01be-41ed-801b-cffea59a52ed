<!DOCTYPE html>
<html lang="ru">
<head>
    <!-- Favicon -->
    <link rel="apple-touch-icon" sizes="180x180" href="/storage/images/favicon/apple-touch-icon.png">
    <link rel="icon" type="image/png" sizes="32x32" href="/storage/images/favicon/favicon-16x16.png">
    <link rel="icon" type="image/png" sizes="16x16" href="/storage/images/favicon/favicon-32x32.png">
    <link rel="manifest" href="/storage/images/favicon/site.webmanifest">

    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title>VPN Подписка</title>

    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- QR Code generator - using qrious library (more reliable) -->
    <script src="https://cdn.jsdelivr.net/npm/qrious@4.0.2/dist/qrious.min.js" onload="console.log('QRious library loaded:', typeof QRious)" onerror="loadQRCodeFallback()"></script>

    <!-- Fallback QR Code library -->
    <script>
        function loadQRCodeFallback() {
            console.log('Loading QRCode fallback...');
            const script = document.createElement('script');
            script.src = 'https://cdnjs.cloudflare.com/ajax/libs/qrious/4.0.2/qrious.min.js';
            script.onload = () => console.log('QRious fallback loaded:', typeof QRious);
            script.onerror = () => {
                console.error('QRious fallback also failed, trying alternative...');
                loadAlternativeQR();
            };
            document.head.appendChild(script);
        }

        function loadAlternativeQR() {
            console.log('Loading alternative QR library...');
            const script = document.createElement('script');
            script.src = 'https://unpkg.com/qrcode-generator@1.4.4/qrcode.js';
            script.onload = () => {
                console.log('Alternative QR library loaded');
                window.useAlternativeQR = true;
            };
            script.onerror = () => {
                console.error('All QR libraries failed');
                window.QRCodeDisabled = true;
            };
            document.head.appendChild(script);
        }
    </script>

    <style>
        #logo {
            background-image: url('/storage/images/smartvpn-logo.jpg');
            background-size: cover;
        }
        .accordionIcon {
            line-height: inherit;
        }
        .accordion-content {
            transition: max-height 0.3s ease-in-out, opacity 0.3s ease-in-out;
            overflow: hidden;
        }
        .accordion-content.closed {
            max-height: 0 !important;
            opacity: 0 !important;
            padding-top: 0 !important;
            padding-bottom: 0 !important;
        }
        .accordion-content.open {
            max-height: 1000px !important;
            opacity: 1 !important;
        }

        /* Alternative method using display */
        .accordion-content.hidden {
            display: none !important;
        }
        .accordion-content.visible {
            display: block !important;
        }

        /* QR Code responsive styles */
        .qr-container {
            width: 100%;
            max-width: 100%;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .qr-canvas {
            max-width: 100%;
            max-height: 100%;
            width: auto;
            height: auto;
            border-radius: 8px;
        }

        /* Responsive QR sizes */
        @media (max-width: 640px) {
            .qr-canvas {
                max-width: 200px;
                max-height: 200px;
            }
        }

        @media (min-width: 641px) and (max-width: 768px) {
            .qr-canvas {
                max-width: 240px;
                max-height: 240px;
            }
        }

        @media (min-width: 769px) {
            .qr-canvas {
                max-width: 280px;
                max-height: 280px;
            }
        }
    </style>
</head>
<body>
    <div class="min-h-screen bg-gradient-to-br from-gray-900 via-black to-gray-900 flex items-center justify-center p-4">
        <div class="max-w-md w-full bg-gray-800 rounded-2xl shadow-2xl border border-gray-700 overflow-hidden">
            <!-- Header with gradient -->
            <div class="bg-gradient-to-r from-blue-600 to-purple-600 p-6">
                <div class="flex items-center justify-between">
                    <!-- Logo/Avatar -->
                    <div id="logo" class="w-16 h-16 bg-white bg-opacity-20 rounded-full flex items-center justify-center flex-shrink-0 border-2 border-white border-opacity-30">
                    </div>

                    <!-- Status -->
                    <div class="text-right flex-1 ml-4">
                        <p class="text-sm font-medium text-blue-100 mb-1">
                            🔰 ID клиента: <span class="can-copy">{{ $data['client_id'] }}</span>
                        </p>
                        <p class="text-sm font-medium text-white">
                            @if(isset($data['subscription']) && $data['subscription'])
                                Подписка: <span class="text-white-500">{{ $data['subscription']['plan_name'] }}</span>
                            @else
                                Подписка
                            @endif

                            @if($data['status'] === 'active')
                                <span class="text-green-500">активна</span>
                            @elseif($data['status'] === 'expired')
                                <span class="text-red-200">истекла</span>
                            @elseif($data['status'] === 'limited')
                                <span class="text-yellow-200">лимит исчерпан</span>
                            @else
                                <span class="text-gray-200">неизвестно</span>
                            @endif
                        </p>
                        @if(isset($data['subscription']) && $data['subscription'])
                            <p class="text-xs text-blue-100 mt-1">
                                @if($data['subscription']['traffic_limit_gb'])
                                    Трафик: {{ number_format(($data['up'] + $data['down']) / 1073741824, 2) }} / {{ $data['subscription']['traffic_limit_gb'] }} GB
                                    @if($data['subscription']['traffic_usage_percentage'])
                                        ({{ number_format($data['subscription']['traffic_usage_percentage'], 1) }}%)
                                    @endif
                                @else
                                    Трафик: {{ number_format(($data['up'] + $data['down']) / 1073741824, 2) }} GB / ∞
                                @endif
                            </p>
                        @endif
                        <p id="expiryDate" class="text-xs text-blue-100"></p>
                        <p id="remainingTime" class="text-xs text-blue-100" style="display: none;"></p>
                    </div>
                </div>
                {{-- Если подписка истекает в течение 3 дней или же подписка Demo, то показываем кнопку продления --}}
                @if($data['subscription']['renewal_required'])
                    <a
                        href="{{ config('app.url') }}/subs/{{ $data['uuid'] }}/renew"
                        class="w-full mt-3 bg-red-500 hover:bg-red-700 text-white font-semibold py-3 px-6 rounded-2xl transition duration-300 text-center block"
                        target="_blank"
                    >
                        <i class="fas fa-credit-card mr-2"></i>
                        {{-- Если Демо, то купить, иначе продлить --}}
                        @if($data['subscription']['is_demo'])
                            Купить подписку
                        @else
                            Продлить подписку
                        @endif
                    </a>
                @endif
            </div>

            <div class="flex flex-col p-6 space-y-6">

                <!-- Title -->
                <h1 class="text-2xl font-bold text-center text-white">
                    Давайте настроим VPN
                </h1>

                <!-- Steps -->
                <div class="space-y-4 w-full">
                    <div class="flex items-center space-x-4">
                        <div class="flex-shrink-0 w-8 h-8 rounded-full bg-blue-600 flex items-center justify-center">
                            <span class="text-white font-bold text-sm">1</span>
                        </div>
                        <p class="text-sm text-gray-300">Установите приложение, <span class="text-gray-400">но не открывайте его</span></p>
                    </div>

                    <div class="flex items-center space-x-4">
                        <div class="flex-shrink-0 w-8 h-8 rounded-full bg-blue-600 flex items-center justify-center">
                            <span class="text-white font-bold text-sm">2</span>
                        </div>
                        <p class="text-sm text-gray-300">Вернитесь из <span id="storeSourcesList" class="text-blue-400"></span> обратно на эту страницу</p>
                    </div>

                    <div class="flex items-center space-x-4">
                        <div class="flex-shrink-0 w-8 h-8 rounded-full bg-blue-600 flex items-center justify-center">
                            <span class="text-white font-bold text-sm">3</span>
                        </div>
                        <p class="text-sm text-gray-300">Нажмите на кнопку <a id="setupLink1" href="#" class="text-green-400 hover:text-green-300 underline">Завершить настройку</a></p>
                    </div>
                </div>

                <!-- Install Buttons -->
                <div class="space-y-3">
                    <a
                        href="#"
                        id="installButton"
                        target="_blank"
                        rel="noopener noreferrer"
                        class="w-full bg-blue-600 hover:bg-blue-700 text-white font-semibold py-3 px-6 rounded-2xl transition duration-300 text-center block"
                    >
                        <i class="fas fa-download mr-2"></i>
                        <span id="installButtonText">Скачать приложение</span>
                    </a>

                    <a
                        href="#"
                        id="installButtonSecondary"
                        target="_blank"
                        rel="noopener noreferrer"
                        class="w-full bg-gray-600 hover:bg-gray-700 text-white font-semibold py-3 px-6 rounded-2xl transition duration-300 text-center block"
                        style="display: none;"
                    >
                        <i class="fas fa-download mr-2"></i>
                        <span id="installButtonSecondaryText">Скачать из другого источника</span>
                    </a>
                </div>

                <!-- Setup Button -->
                <a
                    id="setupButton"
                    href="#"
                    class="w-full bg-green-600 hover:bg-green-700 text-white font-semibold py-3 px-6 rounded-2xl transition duration-300 text-center block"
                >
                    <i class="fas fa-cog mr-2"></i>
                    <span id="setupButtonText">Завершить настройку</span>
                </a>

                <!-- Support Link -->
                <a
                    href="/support/{{ $data['uuid'] }}"
                    class="w-full bg-yellow-600 hover:bg-yellow-500 text-white py-2 px-4 rounded-lg transition duration-300 text-sm block text-center"
                >
                    <i class="fas fa-headset mr-2"></i>
                    Поддержка
                </a>

                <!-- Additional Options -->
                <div class="w-full">
                    <button
                        id="accordionButtonAdditional"
                        class="flex justify-between w-full px-4 py-2 text-sm font-medium text-left text-gray-300 bg-gray-700 rounded-lg hover:bg-gray-600 focus:outline-none"
                    >
                        <span>Дополнительно: QR, конфигурации</span>
                        <i id="accordionIconAdditional" class="fas fa-chevron-down text-gray-400 accordionIcon"></i>
                    </button>

                    <div id="accordionContentAdditional" class="accordion-content closed hidden px-4 pt-4 pb-2 text-sm text-gray-300 space-y-3" style="display: none; max-height: 0; opacity: 0;">
                        <!-- QR Code Button (only for desktop) -->
                        <button
                            id="qrButton"
                            class="w-full bg-purple-600 hover:bg-purple-500 disabled:bg-purple-400 text-white py-2 px-4 rounded-lg transition duration-300 text-sm"
                        >
                            <i class="fas fa-qrcode mr-2"></i>
                            <span id="qrButtonText">Показать QR код</span>
                        </button>

                        <!-- QR Code Display -->
                        <div id="qrDisplay" class="flex flex-col items-center space-y-3 p-4 bg-white rounded-lg text-center" style="display: none;">
                            <div class="qr-container">
                                <canvas id="qrCanvas" class="qr-canvas"></canvas>
                            </div>
                            <p class="text-xs text-gray-600 text-center max-w-xs">
                                Отсканируйте QR код в приложении для быстрой настройки
                            </p>
                            <button
                                id="hideQrButton"
                                class="text-gray-500 hover:text-gray-700 text-xs px-3 py-1 rounded hover:bg-gray-100 transition-colors"
                            >
                                Скрыть QR код
                            </button>
                        </div>

                        <!-- Copy Subscription Link -->
                        <button
                            id="copySubscriptionButton"
                            class="w-full bg-gray-600 hover:bg-gray-500 text-white py-2 px-4 rounded-lg transition duration-300 text-sm"
                        >
                            <i class="fas fa-link mr-2"></i>
                            <span id="copySubscriptionText">Скопировать ссылку на подписку</span>
                        </button>

                        <!-- Copy VLESS Config -->
                        <button
                            id="copyVlessButton"
                            class="w-full bg-gray-600 hover:bg-gray-500 text-white py-2 px-4 rounded-lg transition duration-300 text-sm"
                        >
                            <i class="fas fa-code mr-2"></i>
                            <span id="copyVlessText">Скопировать конфигурации vless</span>
                        </button>

                        <!-- Support Link -->
                        <a
                            href="/support/{{ $data['uuid'] }}"
                            class="w-full bg-blue-600 hover:bg-blue-500 text-white py-2 px-4 rounded-lg transition duration-300 text-sm block text-center"
                        >
                            <i class="fas fa-headset mr-2"></i>
                            Поддержка
                        </a>

                        <!-- Classic View Link -->
                        <a
                            href="/subs/{{ $data['uuid'] }}"
                            class="w-full bg-gray-600 hover:bg-gray-500 text-white py-2 px-4 rounded-lg transition duration-300 text-sm block text-center"
                        >
                            <i class="fas fa-eye mr-2"></i>
                            Классический вид
                        </a>
                    </div>
                </div>

                <!-- Routing Options -->
                <div class="w-full">
                    <button
                        id="accordionButtonRouting"
                        class="flex justify-between w-full px-4 py-2 text-sm font-medium text-left text-gray-300 bg-gray-700 rounded-lg hover:bg-gray-600 focus:outline-none"
                    >
                        <span>Настройки правил для VPN</span>
                        <i id="accordionIconRouting" class="fas fa-chevron-down text-gray-400 accordionIcon"></i>
                    </button>

                    <div id="accordionContentRouting" class="accordion-content closed hidden px-4 pt-4 pb-2 text-sm text-gray-300 space-y-3" style="display: none; max-height: 0; opacity: 0;">
                        <!-- Routing Preference Checkbox -->
                        <div class="flex items-center justify-between p-3 bg-gray-700 rounded-lg">
                            <div class="flex-1">
                                <label for="routingCheckbox" class="text-sm font-medium text-gray-200 cursor-pointer">
                                    Направлять сайты РФ в обход VPN?
                                </label>
                                <p class="text-xs text-white-400 mt-1">
                                    Российские сайты будут открываться напрямую.
                                </p>
                                <p class="text-xs text-yellow-200 mt-1">
                                    Данная настройка не гарантирует работу при использовании приложений, отличных от v2rayTun
                                </p>
                            </div>
                            <div class="flex-shrink-0 ml-3">
                                <input
                                    type="checkbox"
                                    id="routingCheckbox"
                                    class="w-5 h-5 text-blue-600 bg-gray-600 border-gray-500 rounded focus:ring-blue-500 focus:ring-2"
                                    {{ $data['use_common_routing'] ? 'checked' : '' }}
                                >
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Check if QRCode library is loaded
        console.log('Script started. QRCode available:', typeof QRCode);

        // Check for payment status in URL parameters
        const urlParams = new URLSearchParams(window.location.search);
        const paymentStatus = urlParams.get('payment');

        if (paymentStatus === 'success') {
            // Show success message
            setTimeout(() => {
                showToast('Оплата прошла успешно! Подписка активирована.', 'success');
                // Remove payment parameter from URL
                const newUrl = window.location.pathname + window.location.search.replace(/[?&]payment=success/, '');
                window.history.replaceState({}, document.title, newUrl);
            }, 1000);
        } else if (paymentStatus === 'failed') {
            // Show error message
            setTimeout(() => {
                showToast('Оплата не прошла. Попробуйте еще раз или обратитесь в поддержку.', 'error');
                // Remove payment parameter from URL
                const newUrl = window.location.pathname + window.location.search.replace(/[?&]payment=failed/, '');
                window.history.replaceState({}, document.title, newUrl);
            }, 1000);
        }

        // Data from Laravel
        const subscriptionData = {
            clientId: "{{ $data['client_id'] }}",
            email: "{{ $data['email'] }}",
            subscriptionUrl: "{{ $data['subscription_url'] }}",
            vlessContent: `{{ str_replace(["\n", "\r"], ["\\n", "\\r"], $data['vless_content']) }}`,
            status: "{{ $data['status'] }}",
            uuid: "{{ $data['uuid'] }}",
            expiryTime: "{{ $data['expiry_time'] ? $data['expiry_time']->utc()->toISOString() : '' }}",
            remainingTime: @json($data['remaining_time']),
            // New subscription data
            subscription: @json($data['subscription'] ?? null),
            // Traffic data
            upTraffic: {{ $data['up'] ?? 0 }},
            downTraffic: {{ $data['down'] ?? 0 }},
            totalTraffic: {{ $data['total'] ?? 0 }},
            usedTraffic: {{ $data['used_traffic'] ?? 0 }}
        };

        console.log('Subscription data:', subscriptionData);

        // State
        let platform = '';
        let isMobile = false;
        let isGeneratingQR = false;

        // Platform detection
        function detectPlatform() {
            const userAgent = navigator.userAgent.toLowerCase();
            isMobile = /iphone|ipad|ipod|android|blackberry|mini|windows\sce|palm/i.test(userAgent);

            if (/iphone|ipad|ipod/.test(userAgent)) {
                platform = 'ios';
            } else if (/android/.test(userAgent)) {
                platform = 'android';
            } else if (/win/.test(userAgent)) {
                platform = 'windows';
            } else if (/mac/.test(userAgent)) {
                platform = 'macos';
            } else {
                platform = 'desktop';
            }
        }

        // Get platform-specific app link
        function getPlatformSources() {
            switch (platform) {
                case 'ios':
                case 'ipad':
                case 'macos':
                    return {
                        primary: {
                            name: 'App Store',
                            url: 'https://apps.apple.com/ru/app/v2raytun/id6476628951',
                        },
                    };
                case 'android':
                    return {
                        primary: {
                            name: 'Google Play',
                            url: 'https://play.google.com/store/apps/details?id=com.v2raytun.android',
                        },
                        secondary: {
                            name: 'GitHub',
                            url: 'https://github.com/DigneZzZ/v2raytun/releases/latest/download/v2RayTun_universal.apk',
                        },
                    };
                case 'windows':
                    return {
                        primary: {
                            name: 'Официальный сайт',
                            url: 'https://storage.v2raytun.com/v2RayTun_Setup.exe',
                        }
                    };
                default:
                    return {
                        primary: {
                            name: 'v2rayTun',
                            url: 'https://v2raytun.com',
                        },
                        secondary: {
                            name: 'Hiddify',
                            url: 'https://github.com/hiddify/hiddify-app/releases/latest',
                        },
                    };
            }
        }

        // Backward compatibility function
        function getPlatformLink() {
            const sources = getPlatformSources();
            return sources.primary.url;
        }

        // Generate sources list for instructions
        function generateSourcesList() {
            const sources = getPlatformSources();
            let html = '';

            if (sources.primary) {
                html += `<a href="${sources.primary.url}" target="_blank" class="text-blue-400 hover:text-blue-300 underline">${sources.primary.name}</a>`;
            }

            if (sources.secondary) {
                html += ` или <a href="${sources.secondary.url}" target="_blank" class="text-blue-400 hover:text-blue-300 underline">${sources.secondary.name}</a>`;
            }

            return html;
        }

        // Get responsive QR code size based on screen width
        function getResponsiveQRSize() {
            const screenWidth = window.innerWidth;
            if (screenWidth <= 640) {
                return 200; // Mobile
            } else if (screenWidth <= 768) {
                return 240; // Tablet
            } else {
                return 280; // Desktop
            }
        }

        // Create deeplink for v2raytun app
        function createDeeplink(subscriptionUrl) {
            if (!subscriptionUrl) {
                console.error('Subscription URL is empty');
                return '#';
            }

            // Create deeplink without encoding
            const deeplink = `v2raytun://import/${subscriptionUrl}`;

            console.log('Created deeplink:', deeplink);
            return deeplink;
        }

        // Auto-launch deeplink to import configuration
        function autoLaunchDeeplink() {
            if (!subscriptionData.subscriptionUrl) {
                console.log('No subscription URL available for auto-launch');
                return;
            }

            const deeplink = createDeeplink(subscriptionData.subscriptionUrl);
            console.log('Auto-launching deeplink:', deeplink);

            try {
                // Create a temporary link and click it
                const tempLink = document.createElement('a');
                tempLink.href = deeplink;
                tempLink.style.display = 'none';
                document.body.appendChild(tempLink);
                tempLink.click();
                document.body.removeChild(tempLink);

                console.log('Deeplink auto-launch attempted');
            } catch (error) {
                console.error('Error auto-launching deeplink:', error);
            }
        }

        // Format date in user's timezone
        function formatExpiryDate(isoString) {
            if (!isoString) return 'Без ограничений';

            try {
                const date = new Date(isoString);
                return date.toLocaleDateString('ru-RU', {
                    year: 'numeric',
                    month: '2-digit',
                    day: '2-digit',
                    hour: '2-digit',
                    minute: '2-digit'
                });
            } catch (error) {
                console.error('Error formatting date:', error);
                return 'Ошибка даты';
            }
        }

        // Calculate remaining time in user's timezone
        function calculateRemainingTime(isoString) {
            if (!isoString) return null;

            try {
                const expiryDate = new Date(isoString);
                const now = new Date();
                const diffMs = expiryDate.getTime() - now.getTime();

                if (diffMs <= 0) {
                    return { expired: true, display: 'истекла' };
                }

                const days = Math.floor(diffMs / (1000 * 60 * 60 * 24));
                const hours = Math.floor((diffMs % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
                const minutes = Math.floor((diffMs % (1000 * 60 * 60)) / (1000 * 60));

                if (days > 0) {
                    return { expired: false, display: `${days} дн.` };
                } else if (hours > 0) {
                    return { expired: false, display: `${hours} ч.` + ((minutes) ? ` ${minutes} мин.` : '') };
                } else if (minutes > 0) {
                    return { expired: false, display: `${minutes} мин.` };
                } else {
                    return { expired: false, display: 'менее минуты' };
                }
            } catch (error) {
                console.error('Error calculating remaining time:', error);
                return null;
            }
        }

        // Update time display in user's timezone
        function updateTimeDisplay() {
            const expiryDateElement = document.getElementById('expiryDate');
            const remainingTimeElement = document.getElementById('remainingTime');

            if (subscriptionData.expiryTime) {
                // Format expiry date in user's timezone
                const formattedDate = formatExpiryDate(subscriptionData.expiryTime);

                // Show "до" only if subscription is not expired
                if (subscriptionData.status === 'expired') {
                    expiryDateElement.textContent = formattedDate;
                } else {
                    expiryDateElement.textContent = `до ${formattedDate}`;
                }

                // Calculate and display remaining time
                const remaining = calculateRemainingTime(subscriptionData.expiryTime);
                if (remaining && !remaining.expired && subscriptionData.status === 'active') {
                    remainingTimeElement.textContent = `осталось ${remaining.display}`;
                    remainingTimeElement.style.display = 'block';
                } else {
                    remainingTimeElement.style.display = 'none';
                }
            } else {
                expiryDateElement.textContent = '';
                remainingTimeElement.style.display = 'none';
            }
        }

        // Copy to clipboard
        async function copyToClipboard(text, buttonId, textId, originalText) {
            try {
                await navigator.clipboard.writeText(text);
                document.getElementById(textId).textContent = 'Скопировано!';
                setTimeout(() => {
                    document.getElementById(textId).textContent = originalText;
                }, 2000);
            } catch (err) {
                console.error('Failed to copy: ', err);
            }
        }

        // Wait for QR library to load
        function waitForQRCode() {
            return new Promise((resolve, reject) => {
                // Check if QR functionality is disabled
                if (window.QRCodeDisabled) {
                    reject(new Error('QR library is disabled'));
                    return;
                }

                // Check for QRious library (primary)
                if (typeof QRious !== 'undefined') {
                    console.log('QRious library found!');
                    resolve('qrious');
                    return;
                }

                // Check for alternative QR library
                if (window.useAlternativeQR && typeof qrcode !== 'undefined') {
                    console.log('Alternative QR library found!');
                    resolve('alternative');
                    return;
                }

                let attempts = 0;
                const maxAttempts = 30; // 3 seconds (reduced)

                const checkInterval = setInterval(() => {
                    attempts++;
                    console.log('Checking for QR library, attempt:', attempts);

                    if (window.QRCodeDisabled) {
                        clearInterval(checkInterval);
                        reject(new Error('QR library is disabled'));
                    } else if (typeof QRious !== 'undefined') {
                        clearInterval(checkInterval);
                        console.log('QRious library found!');
                        resolve('qrious');
                    } else if (window.useAlternativeQR && typeof qrcode !== 'undefined') {
                        clearInterval(checkInterval);
                        console.log('Alternative QR library found!');
                        resolve('alternative');
                    } else if (attempts >= maxAttempts) {
                        clearInterval(checkInterval);
                        reject(new Error('QR library failed to load after 3 seconds'));
                    }
                }, 100);
            });
        }

        // Generate QR code
        async function generateQRCode() {
            console.log('generateQRCode called');
            console.log('isGeneratingQR:', isGeneratingQR);
            console.log('subscriptionData.subscriptionUrl:', subscriptionData.subscriptionUrl);

            if (isGeneratingQR) return;

            isGeneratingQR = true;
            const qrButton = document.getElementById('qrButton');
            const qrButtonText = document.getElementById('qrButtonText');

            console.log('qrButton:', qrButton);
            console.log('qrButtonText:', qrButtonText);

            if (!qrButton || !qrButtonText) {
                console.error('QR button elements not found');
                isGeneratingQR = false;
                return;
            }

            qrButton.disabled = true;
            qrButtonText.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Генерация QR кода...';

            try {
                // Wait for QR library to be available
                const libraryType = await waitForQRCode();
                console.log('Using QR library:', libraryType);

                const canvas = document.getElementById('qrCanvas');
                console.log('canvas:', canvas);

                if (!canvas) {
                    throw new Error('Canvas element not found');
                }

                if (libraryType === 'qrious') {
                    // Use QRious library
                    console.log('QRious object:', typeof QRious);

                    // Calculate responsive size
                    const containerWidth = canvas.parentElement.clientWidth;
                    const maxSize = Math.min(containerWidth - 20, getResponsiveQRSize()); // 20px padding
                    const qrSize = Math.max(200, Math.min(maxSize, 300)); // Min 200px, Max 300px

                    console.log('Container width:', containerWidth, 'QR size:', qrSize);

                    const qr = new QRious({
                        element: canvas,
                        value: subscriptionData.subscriptionUrl,
                        size: qrSize,
                        padding: null, // Let CSS handle padding
                        background: 'white',
                        foreground: 'black'
                    });
                    console.log('QR code generated with QRious');
                } else if (libraryType === 'alternative') {
                    // Use alternative library
                    console.log('Alternative QR library');

                    // Calculate responsive size for alternative library
                    const containerWidth = canvas.parentElement.clientWidth;
                    const maxSize = Math.min(containerWidth - 20, getResponsiveQRSize());
                    const qrSize = Math.max(200, Math.min(maxSize, 300));

                    console.log('Alternative QR size:', qrSize);

                    const qr = qrcode(0, 'M');
                    qr.addData(subscriptionData.subscriptionUrl);
                    qr.make();

                    const ctx = canvas.getContext('2d');
                    const modules = qr.getModuleCount();
                    const cellSize = qrSize / modules;

                    canvas.width = qrSize;
                    canvas.height = qrSize;

                    ctx.fillStyle = '#FFFFFF';
                    ctx.fillRect(0, 0, qrSize, qrSize);
                    ctx.fillStyle = '#000000';

                    for (let row = 0; row < modules; row++) {
                        for (let col = 0; col < modules; col++) {
                            if (qr.isDark(row, col)) {
                                ctx.fillRect(col * cellSize, row * cellSize, cellSize, cellSize);
                            }
                        }
                    }
                    console.log('QR code generated with alternative library');
                } else {
                    throw new Error('No QR library available');
                }

                console.log('QR code generated successfully');
                document.getElementById('qrDisplay').style.display = 'block';
            } catch (err) {
                console.error('Failed to generate QR code: ', err);
                alert('Ошибка генерации QR кода: ' + err.message);
            } finally {
                isGeneratingQR = false;
                qrButton.disabled = false;
                qrButtonText.innerHTML = '<i class="fas fa-qrcode mr-2"></i>Показать QR код для мобильного';
            }
        }

        // Toggle accordion - updated to work with multiple accordions
        function toggleAccordion(button) {
            console.log('toggleAccordion called for button:', button);

            // Find the content element related to this button
            // Assuming the content has ID that matches button ID pattern
            const buttonId = button.id;
            const contentId = buttonId.replace('Button', 'Content');
            const iconId = buttonId.replace('Button', 'Icon');

            const content = document.getElementById(contentId);
            const icon = document.getElementById(iconId);

            console.log('Looking for content:', contentId, 'icon:', iconId);
            console.log('content element:', content);
            console.log('icon element:', icon);

            if (!content || !icon) {
                console.error('Accordion elements not found! Content:', contentId, 'Icon:', iconId);
                return;
            }

            // Check current state
            const isCurrentlyOpen = content.classList.contains('open') || content.style.display === 'block';
            console.log('Current state - isOpen:', isCurrentlyOpen);

            if (!isCurrentlyOpen) {
                console.log('Opening accordion');
                // Opening accordion
                content.classList.remove('closed', 'hidden');
                content.classList.add('open', 'visible');

                // Direct style (fallback)
                content.style.display = 'block';
                content.style.maxHeight = '1000px';
                content.style.opacity = '1';

                icon.classList.remove('fa-chevron-down');
                icon.classList.add('fa-chevron-up');
            } else {
                console.log('Closing accordion');
                // Closing accordion
                content.classList.remove('open', 'visible');
                content.classList.add('closed', 'hidden');

                // Direct style (fallback)
                content.style.display = 'none';
                content.style.maxHeight = '0';
                content.style.opacity = '0';

                icon.classList.remove('fa-chevron-up');
                icon.classList.add('fa-chevron-down');
            }

            console.log('Accordion classes after toggle:', content.className);
            console.log('Accordion style after toggle:', content.style.cssText);
        }

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            console.log('DOM loaded, initializing...');

            detectPlatform();
            console.log('Platform detected:', platform, 'isMobile:', isMobile);

            // Set install buttons links and text
            const sources = getPlatformSources();
            const installButton = document.getElementById('installButton');
            const installButtonText = document.getElementById('installButtonText');
            const installButtonSecondary = document.getElementById('installButtonSecondary');
            const installButtonSecondaryText = document.getElementById('installButtonSecondaryText');

            // Primary button
            if (installButton && sources.primary) {
                installButton.href = sources.primary.url;
                installButtonText.textContent = `Скачать приложение из ${sources.primary.name}`;
                console.log('Primary install button set to:', sources.primary.url);
            }

            // Secondary button
            if (installButtonSecondary && sources.secondary) {
                installButtonSecondary.href = sources.secondary.url;
                installButtonSecondaryText.textContent = `Скачать приложение из ${sources.secondary.name}`;
                installButtonSecondary.style.display = 'block';
                console.log('Secondary install button set to:', sources.secondary.url);
            }

            // Update sources list in instructions
            const storeSourcesList = document.getElementById('storeSourcesList');
            if (storeSourcesList) {
                storeSourcesList.innerHTML = generateSourcesList();
                console.log('Store sources list updated');
            }

            // Set setup button deeplink
            const setupButton = document.getElementById('setupButton');
            if (setupButton && subscriptionData.subscriptionUrl) {
                const deeplink = createDeeplink(subscriptionData.subscriptionUrl);
                setupButton.href = deeplink;
                console.log('Setup button deeplink set to:', deeplink);

                // Add click handler for deeplink
                setupButton.addEventListener('click', function(e) {
                    console.log('Setup button clicked, attempting to open deeplink:', deeplink);
                    // Let the default href behavior handle the deeplink for all platforms
                });

                // Set the same deeplink for setup link in instructions
                const setupLink1 = document.getElementById('setupLink1');
                if (setupLink1) {
                    setupLink1.href = deeplink;
                    console.log('Setup link deeplink set to:', deeplink);
                }
            }

            // Initialize time display
            updateTimeDisplay();

            // Update time display every minute
            setInterval(updateTimeDisplay, 60000);

            // Получаем значение параметра "autolaunch" из URL
            let params = new URLSearchParams(window.location.search);
            let autolaunch = params.get('autolaunch');

            if (!autolaunch || autolaunch !== 'no') {
                // Auto-launch deeplink after 1 second
                setTimeout(function() {
                    console.log('Auto-launching deeplink after 1 second...');
                    autoLaunchDeeplink();
                }, 1000);
            }

            // Show QR button only for desktop
            const qrButton = document.getElementById('qrButton');
            console.log('QR button element:', qrButton);

            // Event listeners for accordion buttons
            const accordionButtons = document.querySelectorAll('button[id^="accordionButton"]');
            console.log('Found accordion buttons:', accordionButtons.length);

            if (accordionButtons.length > 0) {
                console.log('Setting up accordion button event listeners');
                accordionButtons.forEach(accordionButton => {
                    console.log('Adding event listener to button:', accordionButton.id);
                    accordionButton.addEventListener('click', function(e) {
                        console.log('Accordion button clicked:', this.id);
                        e.preventDefault();
                        toggleAccordion(this);
                    });
                });
            } else {
                console.warn('No accordion buttons found for event listeners!');
            }

            document.getElementById('setupButton').addEventListener('click', function() {
                copyToClipboard(subscriptionData.subscriptionUrl, 'setupButton', 'setupButtonText', 'Завершить настройку');
            });

            const qrButtonForEvent = document.getElementById('qrButton');
            if (qrButtonForEvent) {
                console.log('Adding click event listener to QR button');
                qrButtonForEvent.addEventListener('click', function(e) {
                    console.log('QR button clicked!', e);
                    generateQRCode();
                });
            } else {
                console.error('QR button not found for event listener!');
            }

            document.getElementById('hideQrButton').addEventListener('click', function() {
                document.getElementById('qrDisplay').style.display = 'none';
            });

            document.getElementById('copySubscriptionButton').addEventListener('click', function() {
                copyToClipboard(subscriptionData.subscriptionUrl, 'copySubscriptionButton', 'copySubscriptionText', 'Скопировать ссылку на подписку');
            });

            document.getElementById('copyVlessButton').addEventListener('click', function() {
                copyToClipboard(subscriptionData.vlessContent, 'copyVlessButton', 'copyVlessText', 'Скопировать конфигурации vless');
            });

            // Handle window resize for QR code responsiveness
            let resizeTimeout;
            window.addEventListener('resize', function() {
                clearTimeout(resizeTimeout);
                resizeTimeout = setTimeout(function() {
                    const qrDisplay = document.getElementById('qrDisplay');
                    if (qrDisplay && qrDisplay.style.display !== 'none') {
                        console.log('Window resized, regenerating QR code...');
                        // Hide current QR and regenerate
                        qrDisplay.style.display = 'none';
                        setTimeout(() => {
                            generateQRCode();
                        }, 100);
                    }
                }, 300); // Debounce resize events
            });
        });
    </script>
        <script>
        (function enableCopyTooltip() {
        const tooltipClasses = [
            'tooltip',
            'absolute',
            'left-1/2',
            '-translate-x-1/2',
            'bottom-full',
            'mb-1',
            'text-xs',
            'text-white',
            'bg-black',
            'px-2',
            'py-1',
            'rounded',
            'opacity-0',
            'transition-opacity',
            'duration-300',
            'pointer-events-none',
            'z-10'
        ];

        const style = document.createElement('style');
        style.textContent = `
            .show-tooltip {
            opacity: 1 !important;
            }
        `;
        document.head.appendChild(style);

        document.querySelectorAll('.can-copy').forEach(elem => {
            elem.classList.add('cursor-pointer', 'relative', 'inline-block');

            // Создаем тултип, если его нет
            let tooltip = elem.querySelector('.tooltip');
            if (!tooltip) {
            tooltip = document.createElement('span');
            tooltip.classList.add(...tooltipClasses);
            tooltip.textContent = 'Скопировано';
            elem.appendChild(tooltip);
            }

            elem.addEventListener('click', () => {
            // Временное скрытие тултипа для корректного копирования
            tooltip.style.display = 'none';

            // Получаем только текст без тултипа
            const range = document.createRange();
            range.selectNodeContents(elem);
            const selection = window.getSelection();
            selection.removeAllRanges();
            selection.addRange(range);
            const text = selection.toString().trim();

            // Восстанавливаем тултип
            tooltip.style.display = '';

            // Очищаем выделение
            selection.removeAllRanges();

            // Копируем текст
            navigator.clipboard.writeText(text).then(() => {
                tooltip.classList.add('show-tooltip');
                setTimeout(() => tooltip.classList.remove('show-tooltip'), 2000);
            });
            });
        });
        })();

        // Routing preference checkbox handler
        const routingCheckbox = document.getElementById('routingCheckbox');
        if (routingCheckbox) {
            routingCheckbox.addEventListener('change', async function() {
                const isChecked = this.checked;

                try {
                    // Show loading state
                    this.disabled = true;

                    const response = await fetch(`/subs/{{ $data['uuid'] }}/routing-preference`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || ''
                        },
                        body: JSON.stringify({
                            use_common_routing: isChecked
                        })
                    });

                    const data = await response.json();

                    if (data.success) {
                        // Show success feedback
                        showToast(data.message || 'Настройка обновлена', 'success');
                    } else {
                        // Revert checkbox state on error
                        this.checked = !isChecked;
                        showToast(data.message || 'Ошибка при обновлении настройки', 'error');
                    }
                } catch (error) {
                    console.error('Error updating routing preference:', error);
                    // Revert checkbox state on error
                    this.checked = !isChecked;
                    showToast('Ошибка при обновлении настройки', 'error');
                } finally {
                    // Remove loading state
                    this.disabled = false;
                }
            });
        }

        // Toast notification function
        function showToast(message, type = 'success') {
            // Remove existing toasts
            const existingToasts = document.querySelectorAll('.toast-notification');
            existingToasts.forEach(toast => toast.remove());

            // Create toast element
            const toast = document.createElement('div');
            toast.className = `toast-notification fixed top-4 right-4 z-50 px-4 py-3 rounded-lg text-white font-medium shadow-lg transform transition-all duration-300 translate-x-full`;

            if (type === 'success') {
                toast.classList.add('bg-green-600');
                toast.innerHTML = `<i class="fas fa-check-circle mr-2"></i>${message}`;
            } else {
                toast.classList.add('bg-red-600');
                toast.innerHTML = `<i class="fas fa-exclamation-circle mr-2"></i>${message}`;
            }

            document.body.appendChild(toast);

            // Show toast
            setTimeout(() => {
                toast.classList.remove('translate-x-full');
            }, 100);

            // Hide toast after 3 seconds
            setTimeout(() => {
                toast.classList.add('translate-x-full');
                setTimeout(() => toast.remove(), 300);
            }, 3000);
        }
    </script>
</body>
</html>
