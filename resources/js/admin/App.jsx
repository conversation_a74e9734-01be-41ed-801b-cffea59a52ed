import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { Box } from '@mui/material';

// Snackbar provider
import { SnackbarProvider } from './contexts/SnackbarContext.jsx';
import GlobalErrorHandler from './components/GlobalErrorHandler.jsx';

// Theme
import AppTheme from './theme/AppTheme';

// Components
import Sidebar from './components/Sidebar';
import Dashboard from './pages/Dashboard';
import Users from './pages/Users';
import Servers from './pages/Servers';
import ServerPools from './pages/ServerPools';
import Analytics from './pages/Analytics';
import SystemLogs from './pages/SystemLogs';

function App() {
  const snackbarConfig = {
    autoHideDuration: 5000,
    anchorOrigin: {
      vertical: 'top',
      horizontal: 'center',
    },
    maxSnack: 3,
    preventDuplicate: true,
  };

  return (
    <AppTheme>
      <SnackbarProvider config={snackbarConfig}>
        <GlobalErrorHandler />
        <Router>
          <Box sx={{ display: 'flex' }}>
            <Sidebar />
            <Box
              component="main"
              sx={{
                flexGrow: 1,
                bgcolor: 'background.default',
                p: 3,
                minHeight: '100vh',
              }}
            >
              <Routes>
                <Route path="/admin" element={<Dashboard />} />
                <Route path="/admin/dashboard" element={<Dashboard />} />
                <Route path="/admin/users" element={<Users />} />
                <Route path="/admin/servers" element={<Servers />} />
                <Route path="/admin/server-pools" element={<ServerPools />} />
                <Route path="/admin/analytics" element={<Analytics />} />
                <Route path="/admin/logs" element={<SystemLogs />} />
              </Routes>
            </Box>
          </Box>
        </Router>
      </SnackbarProvider>
    </AppTheme>
  );
}

export default App;
