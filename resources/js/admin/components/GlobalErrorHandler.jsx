import { useEffect } from 'react';
import { useCustomSnackbar } from '../hooks/useCustomSnackbar.jsx';
import { setGlobalErrorHandler } from '../services/apiService.js';

/**
 * Component to initialize global error handling for API requests
 */
function GlobalErrorHandler() {
    const { handleApiResponse } = useCustomSnackbar();

    useEffect(() => {
        // Set the global error handler for API service
        setGlobalErrorHandler(handleApiResponse);

        // Cleanup function to remove handler when component unmounts
        return () => {
            setGlobalErrorHandler(null);
        };
    }, [handleApiResponse]);

    // This component doesn't render anything
    return null;
}

export default GlobalErrorHandler;
