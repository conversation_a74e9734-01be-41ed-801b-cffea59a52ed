import React, { useState, useEffect } from 'react';
import {
  <PERSON>alog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  Grid,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Alert,
  CircularProgress,
  Box,
  Typography,
} from '@mui/material';
import { apiService } from '../services/apiService';
import useCustomSnackbar from '../hooks/useCustomSnackbar';

function UserForm({ open, onClose, user = null, onSuccess }) {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    tg_id: '',
    comment: '',
    referral_code: '',
  });
  const [subscriptionPlans, setSubscriptionPlans] = useState([]);
  const [selectedPlan, setSelectedPlan] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [errors, setErrors] = useState({});

  const isEdit = Boolean(user);

  const {
    showSuccess,
    showError,
    showInfo,
    showLoading,
    closeSnackbar,
    handleApiResponse
  } = useCustomSnackbar();

  useEffect(() => {
    if (user) {
      setFormData({
        name: user.name || '',
        email: user.email || '',
        tg_id: user.tg_id || '',
        comment: user.comment || '',
        referral_code: '',
      });
    } else {
      setFormData({
        name: '',
        email: '',
        tg_id: '',
        comment: '',
        referral_code: '',
      });
    }
    setSelectedPlan('');
    setError(null);
    setErrors({});
  }, [user, open]);

  useEffect(() => {
    if (open) {
      fetchSubscriptionPlans();
    }
  }, [open]);

  const fetchSubscriptionPlans = async () => {
    try {
      const response = await apiService.get('/admin/subscription-plans');
      setSubscriptionPlans(response.data.map(plan => ({
        id: plan.id,
        name: `${plan.name} - ${plan.duration_days} days`,
        price: plan.price_in_cents / 100,
      })));
    } catch (err) {
      console.error('Failed to fetch subscription plans:', err);
      // Fallback to empty array
      setSubscriptionPlans([]);
    }
  };

  const handleInputChange = (field, value) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    // Clear field error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: null }));
    }
  };

  const validateForm = () => {
    const newErrors = {};

    if (!formData.name.trim()) {
      newErrors.name = 'Name is required';
    }

    if (!formData.email.trim()) {
      newErrors.email = 'Email is required';
    } else if (!/[\w\d\W]{5,}/.test(formData.email)) {
      newErrors.email = 'Please enter a valid email address';
    }

    if (!isEdit && !selectedPlan) {
      newErrors.plan = 'Please select a subscription plan';
    }

    setError('Form validating errors');
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async () => {
    if (!validateForm()) {
      return;
    }

    try {
      setLoading(true);
      setError(null);

      if (isEdit) {
        // Update user
        if (selectedPlan) {
          await apiService.users.updateSubscription(user.id, selectedPlan);
        }
        // Note: User info update would need a separate endpoint
      } else {
        // Create new user
        console.log(formData)
        await apiService.users.create(formData);

        // If a plan is selected, we would need to create subscription
        // This would typically be handled by the backend
      }

      onSuccess?.();
      onClose();
    } catch (err) {
      console.error('Form submission error:', err);
      if (err.errors) {
        setErrors(err.errors);
      } else {
        setError(err.message || 'An error occurred while saving the user');
      }
    } finally {
      setLoading(false);
    }
  };

  return (
    <Dialog open={open} onClose={onClose} maxWidth="md" fullWidth>
      <DialogTitle>
        {isEdit ? `Edit User: ${user?.email}` : 'Create New User'}
      </DialogTitle>

      <DialogContent>
        {error && (
          <Alert severity="error" sx={{ mb: 2 }}>
            {error}
          </Alert>
        )}

        <Grid container spacing={2} sx={{ mt: 1 }}>
          <Grid item xs={12} sm={6}>
            <TextField
              fullWidth
              label="Full Name"
              value={formData.name}
              onChange={(e) => handleInputChange('name', e.target.value)}
              error={Boolean(errors.name)}
              helperText={errors.name}
              disabled={loading}
            />
          </Grid>

          <Grid item xs={12} sm={6}>
            <TextField
              fullWidth
              label="Email Address"
              type="text"
              value={formData.email}
              onChange={(e) => handleInputChange('email', e.target.value)}
              error={Boolean(errors.email)}
              helperText={errors.email}
              disabled={loading || isEdit}
            />
          </Grid>

          <Grid item xs={12} sm={6}>
            <TextField
              fullWidth
              label="Telegram ID"
              value={formData.tg_id}
              onChange={(e) => handleInputChange('tg_id', e.target.value)}
              error={Boolean(errors.tg_id)}
              helperText={errors.tg_id}
              disabled={loading}
            />
          </Grid>

          <Grid item xs={12} sm={6}>
            <TextField
              fullWidth
              label="Referral Code"
              value={formData.referral_code}
              onChange={(e) => handleInputChange('referral_code', e.target.value)}
              error={Boolean(errors.referral_code)}
              helperText={errors.referral_code}
              disabled={loading}
            />
          </Grid>

          <Grid item xs={12}>
            <TextField
              fullWidth
              label="Comment"
              multiline
              rows={3}
              value={formData.comment}
              onChange={(e) => handleInputChange('comment', e.target.value)}
              error={Boolean(errors.comment)}
              helperText={errors.comment}
              disabled={loading}
            />
          </Grid>

          <Grid item xs={12} size={6}>
          {subscriptionPlans.length > 0 ?
              <FormControl fullWidth error={Boolean(errors.plan)}>
                <InputLabel>
                  {isEdit ? 'Update Subscription Plan (Optional)' : 'Subscription Plan'}
                </InputLabel>
                <Select
                  value={selectedPlan}
                  label={isEdit ? 'Update Subscription Plan (Optional)' : 'Subscription Plan'}
                  onChange={(e) => {
                    setSelectedPlan(e.target.value)
                    handleInputChange('subscription_plan_id', e.target.value);
                  }}
                  disabled={loading}
                >
                  {isEdit && (
                    <MenuItem value="">
                      <em>Keep current subscription</em>
                    </MenuItem>
                  )}
                  {subscriptionPlans.map((plan) => (
                    <MenuItem key={plan.id} value={plan.id}>
                      {plan.name} - {plan.price} RUB
                    </MenuItem>
                  ))}
                </Select>
                {errors.plan && (
                  <Typography variant="caption" color="error" sx={{ mt: 0.5, ml: 1.5 }}>
                    {errors.plan}
                  </Typography>
                )}
              </FormControl>
              : <CircularProgress /> }
          </Grid>
        </Grid>
      </DialogContent>

      <DialogActions>
        <Button onClick={onClose} disabled={loading}>
          Cancel
        </Button>
        <Button
          onClick={handleSubmit}
          variant="contained"
          disabled={loading}
          startIcon={loading ? <CircularProgress size={20} /> : null}
        >
          {loading ? 'Saving...' : (isEdit ? 'Update User' : 'Create User')}
        </Button>
      </DialogActions>
    </Dialog>
  );
}

export default UserForm;
