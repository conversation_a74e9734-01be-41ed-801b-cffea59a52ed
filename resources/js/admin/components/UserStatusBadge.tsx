import { Badge, Box } from '@mui/material';
import { styled, keyframes } from '@mui/system';

const pulseAnimation = keyframes`
  0%, 50%, 100% {
    transform: scale(1);
    opacity: 1;
  }
  10% {
    transform: scale(1.5);
    opacity: 0.2;
  }
`;

// Стилизованный компонент точки
const OnlineDot = styled('span')(({ theme, online }: { online: boolean }) => ({
  position: 'relative',
  top: 1,
  display: 'inline-block',
  width: 10,
  height: 10,
  borderRadius: '50%',
  backgroundColor: online ? '#00aa66' : '#9e9e9e',
  ...(online && {
    animation: `${pulseAnimation} 1.2s linear infinite`,
  }),
}));

// Обертка для аватарки или любого контента
export default function UserStatusBadge({ online, children }: { online: boolean, children: React.ReactNode }) {
    console.log(online)
  return (
    <Badge
      overlap="circular"
      anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
      badgeContent={<OnlineDot online={online} />}
    >
      {children}
    </Badge>
  );
}
