import React from 'react';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import CssBaseline from '@mui/material/CssBaseline';

// Color palette
const brand = {
  50: '#e3f2fd',
  100: '#bbdefb',
  200: '#90caf9',
  300: '#64b5f6',
  400: '#42a5f5',
  500: '#2196f3',
  600: '#1e88e5',
  700: '#1976d2',
  800: '#1565c0',
  900: '#0d47a1',
};

const gray = {
  50: '#fafafa',
  100: '#f5f5f5',
  200: '#eeeeee',
  300: '#e0e0e0',
  400: '#bdbdbd',
  500: '#9e9e9e',
  600: '#757575',
  700: '#616161',
  800: '#424242',
  900: '#212121',
};

const success = {
  50: '#e8f5e8',
  100: '#c8e6c9',
  200: '#a5d6a7',
  300: '#81c784',
  400: '#66bb6a',
  500: '#4caf50',
  600: '#43a047',
  700: '#388e3c',
  800: '#2e7d32',
  900: '#1b5e20',
};

const warning = {
  50: '#fff8e1',
  100: '#ffecb3',
  200: '#ffe082',
  300: '#ffd54f',
  400: '#ffca28',
  500: '#ffc107',
  600: '#ffb300',
  700: '#ffa000',
  800: '#ff8f00',
  900: '#ff6f00',
};

const error = {
  50: '#ffebee',
  100: '#ffcdd2',
  200: '#ef9a9a',
  300: '#e57373',
  400: '#ef5350',
  500: '#f44336',
  600: '#e53935',
  700: '#d32f2f',
  800: '#c62828',
  900: '#b71c1c',
};

function AppTheme({ children }) {
  const theme = React.useMemo(() => {
    return createTheme({
      palette: {
        mode: 'light',
        primary: { ...brand, main: brand[600] },
        secondary: { main: '#9c27b0' },
        success: { ...success, main: success[600] },
        warning: { ...warning, main: warning[600] },
        error: { ...error, main: error[600] },
        grey: gray,
        background: {
          default: '#f8fafc',
          paper: '#ffffff',
        },
        text: {
          primary: gray[900],
          secondary: gray[700],
        },
        divider: gray[200],
      },
      typography: {
        fontFamily: '"Inter", "Roboto", "Helvetica", "Arial", sans-serif',
        fontSize: 13,
        h1: { fontSize: '2rem', fontWeight: 700, lineHeight: 1.2 },
        h2: { fontSize: '1.75rem', fontWeight: 700, lineHeight: 1.3 },
        h3: { fontSize: '1.5rem', fontWeight: 600, lineHeight: 1.4 },
        h4: { fontSize: '1.25rem', fontWeight: 600, lineHeight: 1.4 },
        h5: { fontSize: '1.125rem', fontWeight: 600, lineHeight: 1.5 },
        h6: { fontSize: '1rem', fontWeight: 600, lineHeight: 1.5 },
        body1: { fontSize: '0.875rem', lineHeight: 1.6 },
        body2: { fontSize: '0.75rem', lineHeight: 1.6 },
      },
      shape: {
        borderRadius: 10,
      },
      components: {
        MuiCssBaseline: {
          styleOverrides: {
            body: {
              backgroundColor: '#f8fafc',
            },
          },
        },
        MuiButton: {
          defaultProps: {
            size: 'small',
          },
          styleOverrides: {
            root: {
              borderRadius: 8,
              textTransform: 'none',
              fontWeight: 600,
              fontSize: '0.75rem',
              padding: '6px 12px',
              boxShadow: 'none',
              '&:hover': {
                boxShadow: '0px 4px 12px rgba(0, 0, 0, 0.15)',
              },
            },
          },
        },
        MuiTextField: {
          defaultProps: {
            size: 'small',
            margin: 'dense',
          },
        },
        MuiFormControl: {
          defaultProps: {
            margin: 'dense',
          },
        },
        MuiListItem: {
          defaultProps: {
            dense: true,
          },
        },
        MuiTable: {
          defaultProps: {
            size: 'small',
          },
        },
        MuiCard: {
          styleOverrides: {
            root: {
              borderRadius: 12,
              boxShadow: '0px 4px 20px rgba(0, 0, 0, 0.05)',
              border: `1px solid ${gray[200]}`,
            },
          },
        },
        MuiDrawer: {
          styleOverrides: {
            paper: {
              backgroundColor: '#ffffff',
              borderRight: `1px solid ${gray[200]}`,
              boxShadow: '4px 0px 20px rgba(0, 0, 0, 0.05)',
            },
          },
        },
        MuiListItemButton: {
          styleOverrides: {
            root: {
              borderRadius: 8,
              margin: '4px 8px',
              '&.Mui-selected': {
                backgroundColor: brand[50],
                color: brand[700],
                '&:hover': {
                  backgroundColor: brand[100],
                },
                '& .MuiListItemIcon-root': {
                  color: brand[700],
                },
              },
              '&:hover': {
                backgroundColor: gray[50],
              },
            },
          },
        },
        MuiChip: {
          styleOverrides: {
            root: {
              borderRadius: 6,
              fontWeight: 500,
              fontSize: '0.75rem',
              height: 24,
            },
          },
        },
        MuiDataGrid: {
          styleOverrides: {
            root: {
              fontSize: '0.75rem',
              border: 'none',
              '& .MuiDataGrid-cell': {
                borderBottom: `1px solid ${gray[200]}`,
              },
              '& .MuiDataGrid-columnHeaders': {
                backgroundColor: gray[50],
                borderBottom: `2px solid ${gray[200]}`,
              },
            },
          },
        },
        MuiTable: {
            defaultProps: {
                size: 'small', // компактный размер по умолчанию
            },
        },
      },
    });
  }, []);

  return (
    <ThemeProvider theme={theme}>
      <CssBaseline />
      {children}
    </ThemeProvider>
  );
}

export default AppTheme;
