import axios from 'axios';

// Global error handler function (will be set by App component)
let globalErrorHandler = null;

// Function to set the global error handler
export const setGlobalErrorHandler = (handler) => {
  globalErrorHandler = handler;
};

// Create axios instance with base configuration
const apiClient = axios.create({
  baseURL: '/api',
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
    'X-Requested-With': 'XMLHttpRequest',
  },
});

// Request interceptor
apiClient.interceptors.request.use(
  (config) => {
    // Add CSRF token if available
    const token = document.querySelector('meta[name="csrf-token"]');
    if (token) {
      config.headers['X-CSRF-TOKEN'] = token.getAttribute('content');
    }

    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor
apiClient.interceptors.response.use(
  (response) => {
    return response.data;
  },
  (error) => {
    if (error.response) {
      // Server responded with error status
      const { status, data } = error.response;

      // Call global error handler if available
      if (globalErrorHandler) {
        globalErrorHandler(error.response.data);
      }

      switch (status) {
        case 401:
          // Unauthorized - redirect to login
          window.location.href = '/login';
          break;
        case 403:
          // Forbidden
          console.error('Access forbidden');
          break;
        case 422:
          // Validation error
          console.error('Validation error:', data.errors);
          break;
        case 500:
          // Server error
          console.error('Server error:', data.message);
          break;
        default:
          console.error('API error:', data.message || 'Unknown error');
      }

      return Promise.reject(data);
    } else if (error.request) {
      // Network error
      console.error('Network error:', error.message);
      return Promise.reject({ message: 'Network error. Please check your connection.' });
    } else {
      // Other error
      console.error('Error:', error.message);
      return Promise.reject({ message: error.message });
    }
  }
);

// API service methods
export const apiService = {
  // Generic methods
  get: (url, config = {}) => apiClient.get(url, config),
  post: (url, data = {}, config = {}) => apiClient.post(url, data, config),
  put: (url, data = {}, config = {}) => apiClient.put(url, data, config),
  delete: (url, config = {}) => apiClient.delete(url, config),
  patch: (url, data = {}, config = {}) => apiClient.patch(url, data, config),

  // User management
  users: {
    list: (params = {}) => apiClient.get('/admin/users', { params }),
    get: (userId) => apiClient.get(`/admin/users/${userId}`),
    create: (userData) => apiClient.post('/admin/users', userData),
    updateSubscription: (userId, planId) =>
      apiClient.put(`/admin/users/${userId}/subscription`, { plan_id: planId }),
    disable: (userId) => apiClient.post(`/admin/users/${userId}/disable`),
    enable: (userId) => apiClient.post(`/admin/users/${userId}/enable`),
    delete: (userId) => apiClient.delete(`/admin/users/${userId}`),
  },

  // Server management
  servers: {
    list: (params = {}) => apiClient.get('/admin/servers', { params }),
    get: (serverId) => apiClient.get(`/admin/servers/${serverId}`),
    getPools: () => apiClient.get('/admin/servers/pools'),
    createBackup: (serverId) => apiClient.post(`/admin/servers/${serverId}/backup`),
    updateStatus: (serverId, isActive) =>
      apiClient.put(`/admin/servers/${serverId}/status`, { is_active: isActive }),
  },

  // Analytics
  analytics: {
    dashboard: () => apiClient.get('/admin/analytics/dashboard'),
    revenue: (params = {}) => apiClient.get('/admin/analytics/revenue', { params }),
    users: (params = {}) => apiClient.get('/admin/analytics/users', { params }),
    systemLogs: (params = {}) => apiClient.get('/admin/analytics/system-logs', { params }),
    backupHistory: (params = {}) => apiClient.get('/admin/analytics/backup-history', { params }),
  },

  // Payments
  payments: {
    create: (paymentData) => apiClient.post('/payments', paymentData),
    getStatus: (orderId) => apiClient.get(`/payments/${orderId}/status`),
  },
};

export default apiService;
