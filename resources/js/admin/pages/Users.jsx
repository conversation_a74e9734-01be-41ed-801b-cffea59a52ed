import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  Card,
  CardContent,
  Button,
  TextField,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  Grid,
  Chip,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Alert,
  CircularProgress,
  Menu,
  LinearProgress,
  Stack,
  Badge,
  Link,
} from '@mui/material';
import { DataGrid } from '@mui/x-data-grid';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Block as BlockIcon,
  CheckCircle as EnableIcon,
  Delete as DeleteIcon,
  Refresh as RefreshIcon,
  Download as DownloadIcon,
  MoreVert as MoreVertIcon,
  CopyAll,
  OpenInNew,
} from '@mui/icons-material';
import { useTheme } from '@mui/material/styles';
import { apiService } from '../services/apiService';
import ExportDialog from '../components/ExportDialog';
import UserForm from '../components/UserForm';
import UserStatusBadge from '../components/UserStatusBadge';
import moment from 'moment';

function Users() {

  const theme = useTheme();

  const [users, setUsers] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [filters, setFilters] = useState({
    search: '',
    source: '',
    status: '',
  });
  const [pagination, setPagination] = useState({
    page: 0,
    pageSize: 20,
    total: 0,
  });
  const [selectedUser, setSelectedUser] = useState(null);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [exportDialogOpen, setExportDialogOpen] = useState(false);
  const [actionLoading, setActionLoading] = useState(false);
  const [actionName, setActionName] = useState(null);

  const [anchorEl, setAnchorEl] = useState(null);
  const openSubsMenu = Boolean(anchorEl);

  const handleSubsMenuClick = (event) => {
    setAnchorEl(event.currentTarget);
  };

  const handleSubsMenuClose = () => {
    setAnchorEl(null);
  };

  const fetchUsers = async () => {
    try {
      setLoading(true);
      const params = {
        page: pagination.page + 1,
        per_page: pagination.pageSize,
      };

      // Only add non-empty filter values
      if (filters.search && filters.search.trim()) {
        params.search = filters.search.trim();
      }
      if (filters.source && filters.source.trim()) {
        params.source = filters.source.trim();
      }
      if (filters.status && filters.status.trim()) {
        params.status = filters.status.trim();
      }

      const response = await apiService.users.list(params);
      setUsers(response.data);
      setPagination(prev => ({
        ...prev,
        total: response.pagination.total,
      }));
      setError(null);
    } catch (err) {
      setError('Failed to load users');
      console.error('Users error:', err);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchUsers();
  }, [pagination.page, pagination.pageSize, filters]);

  const handleFilterChange = (field, value) => {
    setFilters(prev => ({ ...prev, [field]: value }));
    setPagination(prev => ({ ...prev, page: 0 }));
  };

  const handleUserAction = async (action, userId) => {
    try {
      setActionLoading(true);
      setActionName(action);

      switch (action) {
        case 'disable':
          await apiService.users.disable(userId);
          break;
        case 'enable':
          await apiService.users.enable(userId);
          break;
        case 'delete':
          await apiService.users.delete(userId);
          break;
      }

      await fetchUsers();
      setDialogOpen(false);
      setSelectedUser(null);
    } catch (err) {
      setError(`Failed to ${action} user`);
      console.error(`${action} user error:`, err);
    } finally {
      setActionLoading(false);
      setActionName(null);
    }
  };

  const getStatusChip = (user) => {
    if (user.is_disabled) {
      return <Chip label="Disabled" color="error" size="small" variant="outlined" />;
    }

    if (user.is_demo) {
      return <Chip label="Demo" color="warning" size="small" variant="outlined" />;
    }

    if (user.expired === false) {
      return <Chip label="Active" color="success" size="small" variant="outlined" />;
    }

    return <Chip label="Expired" color="warning" size="small" variant="outlined" />;
  };

  const getSourceChip = (source) => {
    const colors = {
      manual: 'default',
      api: 'primary',
      leadteh: 'secondary',
    };
    return <Chip
        label={source}
        color={colors[source] || 'default'}
        size="small"
        variant="outlined"
    />;
  };

  const formatNumber = (value) => {
    return Number.isInteger(value) ? value.toString() : value.toFixed(1);
  }

  const formatBytes = (bytes) => {
    if (bytes === 0) return '0 B';
    const units = ['Byte', 'Kb', 'Mb', 'Gb', 'Tb'];
    const power = Math.floor(Math.log(bytes) / Math.log(1024));
    const value = bytes / Math.pow(1024, power);
    return `${formatNumber(value)} ` + units[power];
  }

  const getUsageBlock = (user) => {
    let total = user.total_gb;
    let used = user.used_gb;
    let available = total - used;
    let progress = 0;
    let reversed_progress = 100;
    if (total > 0) {
      progress = (used / total) * 100;
      reversed_progress = 100 - progress;
    }

    return (
        <Stack
            direction="column"
            spacing={0}
            sx={{
                width: '100%',
                height: '100%',
                display: 'flex',
            }}
            alignItems="center"
            justifyContent="center"
        >
            { total > 0 ?
                <>
                    <Typography variant="body2">
                        { formatBytes(available) }
                    </Typography>
                    <LinearProgress
                        variant="determinate"
                        value={ reversed_progress }
                        sx={{ width: 100, height: '4px !important' }}
                    />
                    <Typography variant="body2">
                        из { total > 0 ? formatBytes(total) : '∞' }
                    </Typography>
                </> :
                <Typography variant="body2">
                    { formatBytes(available) } / ∞
                </Typography>
            }
            {/* <Box>
                <Badge
                    badgeContent={ total > 0 ? <LinearProgress
                        variant="determinate"
                        value={ progress }
                        sx={{ width: 80, height: 6 }}
                    /> : '∞' }
                    overlap="circular"
                    sx={{
                        '& .MuiBadge-badge': {
                            backgroundColor: total ? theme.palette.warning.main : theme.palette.success[300],
                            color: '#fff',
                        },
                        '& .MuiLinearProgress-root': {
                            height: '6px !important',
                        },
                    }}
                >
                    <Typography variant="body2" sx={{ mr: -1, mt: 1 }}>
                        { usedInGb.toFixed(1) } / { totalInGb.toFixed(1) }
                    </Typography>
                </Badge>
            </Box> */}
        </Stack>
        /* <Typography variant="body2">{ progress.toFixed(1) }%</Typography> */
    );
  }

  /**
   * humanized expires in (momentjs) like 56 days, but if less than a day then hours and if less than a hour then minutes
   */
  const formatHumanizedTime = (dateString) => {
    if (!dateString) return 'Never';
    const diff = moment(dateString).diff(moment(), 'seconds');
    if (diff < 0) return 'Expired';
    if (diff < 60) return `in ${diff} seconds`;
    if (diff < 3600) return `in ${Math.floor(diff / 60)} minutes`;
    if (diff < 86400) return `in ${Math.floor(diff / 3600)} hours`;
    return `in ${Math.floor(diff / 86400)} days`;
  }

  const columns = [
    { field: 'id', headerName: 'ID', width: 70 },
    { field: 'online', headerName: '', width: 10, renderCell: (params) => (
        <UserStatusBadge online={params.row.is_online} />
    ) },
    { field: 'name', headerName: 'Name', width: 100 },
    { field: 'email', headerName: 'Email', width: 180 },
    {
        field: 'tg_id',
        headerName: 'Telegram ID',
        renderCell: (params) => (
            <Link href={ `tg://user?id=${params.value}` }>
                { params.value }
            </Link>
        ),
        width: 80,
    },
    {
      field: 'source',
      headerName: 'Source',
      width: 100,
      renderCell: (params) => getSourceChip(params.value),
    },
    {
      field: 'status',
      headerName: 'Status',
      width: 100,
      renderCell: (params) => getStatusChip(params.row),
    },
    {
      field: 'subscription',
      headerName: 'Plan',
      sortable: false,
      width: 150,
      renderCell: (params) => params.row.current_subscription?.plan_name || 'No subscription',
    },
    {
      field: 'subscription_expires_at',
      headerName: 'Expires',
      width: 140,
      renderCell: (params) => {
        if (params.row.current_subscription?.expires_at === null) return 'Never';
        return formatHumanizedTime(params.row.current_subscription?.expires_at);
      },
    },
    {
      field: 'usage',
      headerName: 'Usage',
      width: 120,
      renderCell: (params) => getUsageBlock(params.row),
    },
    {
      field: 'vless_keys_count',
      headerName: 'Keys',
      sortable: false,
      width: 80,
      renderCell: (params) => (
        <Chip
          label={params.value || 0}
          size="small"
          color={params.value > 0 ? 'success' : 'default'}
          variant="outlined"
        />
      ),
    },
    {
      field: 'subscription_url',
      headerName: 'Subs Link',
      sortable: false,
      width: 100,
      renderCell: (params) => (
        params.value ? (
            <div>
                <IconButton
                    aria-label="more"
                    id="long-button"
                    aria-controls={ openSubsMenu ? 'long-menu' : undefined }
                    aria-expanded={ openSubsMenu ? 'true' : undefined }
                    aria-haspopup="true"
                    onClick={ handleSubsMenuClick }
                >
                    <MoreVertIcon />
                </IconButton>
                <Menu
                    id="long-menu"
                    anchorEl={ anchorEl }
                    open={ openSubsMenu }
                    onClose={ handleSubsMenuClose }
                    slotProps={{
                        paper: {
                            style: {
                            maxHeight: 48 * 4.5,
                            width: '12ch',
                            },
                        },
                        list: {
                            'aria-labelledby': 'long-button',
                        },
                    }}
                >
                    <MenuItem
                        key="copy"
                        onClick={() => {
                            navigator.clipboard.writeText(params.value).then(function() {
                                console.log('Async: Copying to clipboard was successful!');
                            }, function(err) {
                                console.error('Async: Could not copy text: ', err);
                            });
                        }}
                        sx={{ display: 'flex', justifyContent: 'space-between' }}
                    >
                        Copy <CopyAll />
                    </MenuItem>
                    <MenuItem
                        key="open"
                        onClick={() => window.open(params.value, '_blank')}
                        sx={{ display: 'flex', justifyContent: 'space-between' }}
                    >
                        Open <OpenInNew />
                    </MenuItem>
                </Menu>
            </div>
        ) : (
          <Typography variant="caption" color="text.secondary">
            No URL
          </Typography>
        )
      ),
    },
    {
      field: 'created_at',
      headerName: 'Created',
      width: 140,
      renderCell: (params) => new Date(params.value).toLocaleString(),
    },
    {
      field: 'actions',
      headerName: 'Actions',
      width: 200,
      sortable: false,
      renderCell: (params) => (
        <Box>
          <IconButton
            size="small"
            onClick={() => {
              setSelectedUser(params.row);
              setDialogOpen(true);
            }}
            disabled={ actionLoading }
          >
            <EditIcon />
          </IconButton>
          {params.row.is_disabled ? (
            <IconButton
              size="small"
              color="success"
              onClick={() => handleUserAction('enable', params.row.id)}
              disabled={ actionLoading }
            >
              { actionName === 'enable' && actionLoading ? (
                <CircularProgress size={20} />
                ) : (
                <EnableIcon />
                ) }
            </IconButton>
          ) : (
            <IconButton
              size="small"
              color="warning"
              onClick={() => handleUserAction('disable', params.row.id)}
              disabled={ actionLoading }
            >
              { actionName === 'disable' && actionLoading ? (
                <CircularProgress size={20} />
                ) : (
                <BlockIcon />
                ) }
            </IconButton>
          )}
          <IconButton
            size="small"
            color="error"
            onClick={() => {
              if (window.confirm('Are you sure you want to delete this user?')) {
                handleUserAction('delete', params.row.id);
              }
            }}
            disabled={ actionLoading }
          >
            { actionName === 'delete' && actionLoading ? (
              <CircularProgress size={20} />
            ) : (
              <DeleteIcon />
            ) }
          </IconButton>
        </Box>
      ),
    },
  ];

  return (
    <Box>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4" component="h1">
          Users Management
        </Typography>
        <Box>
          <Button
            variant="outlined"
            startIcon={<DownloadIcon />}
            onClick={() => setExportDialogOpen(true)}
            sx={{ mr: 1 }}
          >
            Export
          </Button>
          <Button
            variant="outlined"
            startIcon={<RefreshIcon />}
            onClick={fetchUsers}
            sx={{ mr: 1 }}
          >
            Refresh
          </Button>
          <Button
            variant="contained"
            startIcon={<AddIcon />}
            onClick={() => {
              setSelectedUser(null);
              setDialogOpen(true);
            }}
          >
            Add User
          </Button>
        </Box>
      </Box>

      {error && (
        <Alert severity="error" sx={{ mb: 2 }}>
          {error}
        </Alert>
      )}

      {/* Filters */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Grid container spacing={2}>
            <Grid xs={12} md={4}>
              <TextField
                fullWidth
                label="Search"
                value={filters.search}
                onChange={(e) => handleFilterChange('search', e.target.value)}
                placeholder="Search by name, email, or Telegram ID"
                size="small"
              />
            </Grid>
            <Grid xs={12} md={4}>
              <FormControl fullWidth size="small">
                <InputLabel>Source</InputLabel>
                <Select
                  value={filters.source}
                  label="Source"
                  onChange={(e) => handleFilterChange('source', e.target.value)}
                >
                  <MenuItem value="">All Sources</MenuItem>
                  <MenuItem value="manual">Manual</MenuItem>
                  <MenuItem value="api">API</MenuItem>
                  <MenuItem value="leadteh">LeadTeh</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid xs={12} md={4}>
              <FormControl fullWidth size="small">
                <InputLabel>Status</InputLabel>
                <Select
                  value={filters.status}
                  label="Status"
                  onChange={(e) => handleFilterChange('status', e.target.value)}
                >
                  <MenuItem value="">All Statuses</MenuItem>
                  <MenuItem value="active">Active</MenuItem>
                  <MenuItem value="expired">Expired</MenuItem>
                  <MenuItem value="disabled">Disabled</MenuItem>
                </Select>
              </FormControl>
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      {/* Users Table */}
      <Card>
        <CardContent>
            <DataGrid
                rows={users}
                columns={columns}
                loading={loading}
                pagination
                paginationMode="server"
                rowCount={pagination.total}
                page={pagination.page}
                pageSize={pagination.pageSize}
                onPageChange={(newPage) => setPagination(prev => ({ ...prev, page: newPage }))}
                onPageSizeChange={(newPageSize) => setPagination(prev => ({ ...prev, pageSize: newPageSize }))}
                pageSizeOptions={[10, 20, 50, 100]}
                disableSelectionOnClick
                disableColumnSelector
                autoHeight
                autoPageSize
            />
        </CardContent>
      </Card>

      {/* User Form Dialog */}
      <UserForm
        open={dialogOpen}
        onClose={() => {
          setDialogOpen(false);
          setSelectedUser(null);
        }}
        user={selectedUser}
        onSuccess={() => {
          fetchUsers();
          setDialogOpen(false);
          setSelectedUser(null);
        }}
      />

      {/* Export Dialog */}
      <ExportDialog
        open={exportDialogOpen}
        onClose={() => setExportDialogOpen(false)}
        type="users"
        title="Users Data"
        filters={{ filters }}
      />
    </Box>
  );
}

export default Users;
