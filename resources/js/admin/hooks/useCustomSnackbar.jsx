import React from 'react';
import { useSnackbar } from '../contexts/SnackbarContext.jsx';
import {
    Button,
    IconButton,
    CircularProgress,
    Typography,
} from '@mui/material';
import {
    Close as CloseIcon,
    Refresh as RefreshIcon,
} from '@mui/icons-material';

/**
 * Custom hook that wraps MUI Snackbar with our custom configurations
 * and provides convenient methods for different types of notifications.
 */
export const useCustomSnackbar = () => {
  const { enqueueSnackbar, closeSnackbar, closeAllSnackbars } = useSnackbar();

  // Success notification
  const showSuccess = (message, options = {}) => {
    return enqueueSnackbar(message, {
      variant: 'success',
      autoHideDuration: 4000,
      ...options,
    });
  };

  // Error notification
  const showError = (message, options = {}) => {
    return enqueueSnackbar(message, {
      variant: 'error',
      autoHideDuration: 6000,
      persist: false,
      ...options,
    });
  };

  // Warning notification
  const showWarning = (message, options = {}) => {
    return enqueueSnackbar(message, {
      variant: 'warning',
      autoHideDuration: 5000,
      ...options,
    });
  };

  // Info notification
  const showInfo = (message, options = {}) => {
    return enqueueSnackbar(message, {
      variant: 'info',
      autoHideDuration: 4000,
      ...options,
    });
  };

  // Quick notification (short duration)
  const showQuick = (message, variant = 'default', options = {}) => {
    return enqueueSnackbar(message, {
      variant,
      autoHideDuration: 2000,
      ...options,
    });
  };

  // Persistent notification (manual close)
  const showPersistent = (message, variant = 'default', options = {}) => {
    return enqueueSnackbar(message, {
      variant,
      persist: true,
      action: (
        <IconButton
          size="small"
          color="inherit"
          sx={{ ml: 1 }}
        >
          <CloseIcon fontSize="small" />
        </IconButton>
      ),
      ...options,
    });
  };

  // Loading notification
  const showLoading = (message = 'Loading...', options = {}) => {
    return enqueueSnackbar(message, {
      variant: 'info',
      persist: true,
      action: (
        <CircularProgress
          size={16}
          sx={{
            color: 'inherit',
            ml: 1
          }}
        />
      ),
      ...options,
    });
  };

  // API response handler
  const handleApiResponse = (response, successMessage = 'Operation completed successfully') => {
    if (response.success) {
      showSuccess(response.message || successMessage);
    } else {
      // Проверяем наличие детальных ошибок
      if (response.errors && typeof response.errors === 'object' && Object.keys(response.errors).length > 0) {
        // Создаем React элементы для каждой ошибки
        const errorElements = Object.values(response.errors).flat().map((errorText, index) => (
          <Typography key={index} variant="body2" sx={{ mb: 0.5 }}>
            • {errorText}
          </Typography>
        ));

        // Передаем React элементы как сообщение
        showError(
          <div>{errorElements}</div>,
          { title: response.message || 'Validation Error' }
        );
      } else {
        showError(response.message || 'An error occurred');
      }
    }
  };

  // Validation errors handler
  const showValidationErrors = (errors) => {
    if (typeof errors === 'object' && errors !== null) {
      Object.values(errors).flat().forEach(error => {
        showError(error, { autoHideDuration: 8000 });
      });
    } else if (typeof errors === 'string') {
      showError(errors);
    }
  };

  // Helper to create React element message
  const createReactMessage = (elements) => {
    return (
      <div>
        {elements}
      </div>
    );
  };

  return {
    // Original MUI snackbar methods
    enqueueSnackbar,
    closeSnackbar,
    closeAllSnackbars,

    // Custom convenience methods
    showSuccess,
    showError,
    showWarning,
    showInfo,
    showQuick,
    showPersistent,
    showLoading,

    // Utility methods
    handleApiResponse,
    showValidationErrors,
    createReactMessage,
  };
};

export default useCustomSnackbar;
