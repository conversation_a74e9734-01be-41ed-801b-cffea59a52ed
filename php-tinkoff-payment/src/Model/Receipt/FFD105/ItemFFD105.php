<?php

namespace Pada\Tinkoff\Payment\Model\Receipt\FFD105;

use Pada\Tinkoff\Payment\Constant;
use Pada\Tinkoff\Payment\Model\Receipt\AbstractItem;

final class ItemFFD105 extends AbstractItem
{
    public static function getBuilder(): ItemBuilderFFD105
    {
        return new ItemBuilderFFD105();
    }

    public function setPaymentObject(?string $paymentObject): void
    {
        if (null !== $paymentObject && Constant::paymentObjectInvalidFfd105($paymentObject)) {
            throw new \InvalidArgumentException('Unknown payment object value');
        }
        parent::setPaymentObject($paymentObject); // TODO: Change the autogenerated stub
    }

    public function setTax(string $tax): void
    {
        if (Constant::taxInvalidFfd105($tax)) {
            throw new \InvalidArgumentException('Unknown tax value');
        }
        parent::setTax($tax); // TODO: Change the autogenerated stub
    }
}
