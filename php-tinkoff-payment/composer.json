{"name": "paveldanilin/php-tinkoff-payment", "description": "Tinkoff payment client for PHP 7.4+", "keywords": ["<PERSON><PERSON><PERSON>", "pay", "payment", "kassa"], "type": "library", "license": "MIT", "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "autoload": {"psr-4": {"Pada\\Tinkoff\\Payment\\": "src/"}, "files": ["src/functions.php"]}, "autoload-dev": {"psr-4": {"Pada\\Tinkoff\\Tests\\": "tests"}}, "require": {"php": ">=8.1", "paveldanilin/rest-client": "2.0.*"}, "require-dev": {"phpstan/phpstan": "1.10.*", "phpunit/phpunit": "10.5.*", "monolog/monolog": "2.8.0"}, "scripts": {"test": ["php -d memory_limit=4G ./vendor/bin/phpstan analyse -c phpstan.neon", "php ./vendor/bin/phpunit"]}}