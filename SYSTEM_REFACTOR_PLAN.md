# План полной переработки системы VPN подписок

## Обзор изменений

Система переходит от дублирования данных клиентов локально к централизованному управлению через таблицу `users`. XUI серверы становятся вторичными, управляемыми из основной системы.

## Основные принципы новой архитектуры

1. **Единый источник истины** - таблица `users` содержит всю информацию о подписках
2. **XUI как исполнитель** - серверы XUI только выполняют команды из основной системы
3. **Событийная архитектура** - все действия происходят через события
4. **Пулы серверов** - автоматическое распределение пользователей по доступности
5. **Система тарификации** - гибкие тарифные планы с различными ограничениями

## Этапы реализации

### Этап 1: Подготовка новой структуры БД

#### 1.1 Новые таблицы

**server_pools** - пулы серверов
```sql
- id
- name
- description
- max_users (default: 250)
- is_active
- created_at, updated_at
```

**server_pool_assignments** - привязка серверов к пулам
```sql
- id
- server_pool_id
- xui_server_id
- created_at, updated_at
- unique(server_pool_id, xui_server_id)
```

**subscription_plans** - тарифные планы
```sql
- id
- name
- description
- duration_amount (количество)
- duration_unit (month, day, year)
- traffic_gb (null = безлимит)
- server_count (null = все доступные)
- price_in_cents (цена в копейках, 0 = бесплатно)
- discount_percent (скидка по сроку)
- is_demo (boolean)
- is_active (boolean)
- is_archived (boolean)
- created_at, updated_at
```

**user_subscriptions** - текущие подписки пользователей
```sql
- id
- user_id
- subscription_plan_id
- server_pool_id
- started_at
- expires_at
- traffic_limit_gb
- traffic_used_gb
- is_active
- created_at, updated_at
```

**orders** - заказы/инвойсы
```sql
- id
- user_id
- subscription_plan_id
- amount_in_cents
- currency (default: RUB)
- status (pending, paid, failed, cancelled, expired)
- description
- expires_at
- created_at, updated_at
```

**payments** - платежи
```sql
- id
- order_id
- method (tbank, manual, balance)
- status (pending, processing, completed, failed, cancelled)
- external_id
- external_details (json)
- payment_url
- amount_in_cents
- processed_at
- created_at, updated_at
```

**user_usage_history** - история использования трафика
```sql
- id
- user_id
- xui_inbound_id
- up_traffic
- down_traffic
- recorded_at
- created_at
```

**user_online_history** - история онлайн активности
```sql
- id
- user_id
- xui_inbound_id
- status (online, offline)
- recorded_at
- created_at
```

**inbound_backups** - бэкапы инбаундов
```sql
- id
- xui_server_id
- xui_inbound_id
- backup_data (json)
- created_at
```

**admin_users** - администраторы
```sql
- id
- name
- email
- password
- role (superadmin, moderator, support)
- is_active
- last_login_at
- created_at, updated_at
```

#### 1.2 Изменения в существующих таблицах

**users** - добавить поля:
```sql
- server_pool_id (nullable)
- subscription_plan_id (nullable)
- source (leadteh, manual, api)
- additional_data (json) - для contact_id из leadteh
- traffic_reset_at (когда последний раз сбрасывался трафик)
```

**xui_inbounds** - добавить поля:
```sql
- user_id (для связи с создателем, nullable)
- backup_interval_hours (default: 1)
```

#### 1.3 Удаляемые таблицы
- `xui_clients` - больше не нужна, данные переносятся в users

### Этап 2: Новые сервисы

#### 2.1 XuiManagementService
Расширенный сервис для работы с XUI API:

**Методы для инбаундов:**
- `generateX25519Keys()` - генерация ключей
- `createInbound($server, $inboundData)` - создание инбаунда
- `updateInbound($server, $inboundId, $data)` - обновление инбаунда
- `deleteInbound($server, $inboundId)` - удаление инбаунда
- `importInbound($server, $backupData)` - импорт из бэкапа

**Методы для клиентов:**
- `createClient($server, $inboundId, $clientData)` - создание клиента
- `updateClient($server, $inboundId, $clientId, $data)` - обновление клиента
- `deleteClient($server, $inboundId, $clientId)` - удаление клиента
- `resetClientTraffic($server, $inboundId, $email)` - сброс трафика

**Методы для статистики:**
- `getInboundsList($server)` - получение списка инбаундов
- `getOnlineClients($server)` - клиенты онлайн

#### 2.2 ServerPoolService
Управление пулами серверов:
- `getAvailablePool()` - получить первый доступный пул
- `assignUserToPool($user, $pool)` - назначить пользователя в пул
- `removeUserFromPool($user)` - удалить пользователя из пула
- `getPoolUsage($pool)` - статистика использования пула
- `checkPoolCapacity($pool)` - проверка заполненности

#### 2.3 SubscriptionPlanService
Управление тарифными планами:
- `getAvailablePlans()` - доступные тарифы
- `getDemoplan()` - демо тариф
- `calculatePrice($plan, $duration)` - расчет цены с учетом скидок

#### 2.4 UserSubscriptionService
Управление подписками пользователей:
- `createSubscription($user, $plan, $pool)` - создание подписки
- `renewSubscription($user, $plan)` - продление подписки
- `expireSubscription($user)` - истечение подписки
- `updateTrafficUsage($user, $usage)` - обновление использования трафика

#### 2.5 PaymentService
Система оплат:
- `createOrder($user, $plan)` - создание заказа
- `processPayment($order, $method, $data)` - обработка платежа
- `handleWebhook($data)` - обработка вебхуков от T-Bank

#### 2.6 LeadtehIntegrationService
Интеграция с LeadTeh:
- `updateUserVariables($contactId, $variables)` - обновление переменных
- `processReferralRewards($payment)` - начисление вознаграждений
- `notifyUser($contactId, $message)` - уведомление пользователя

#### 2.7 BackupService
Система бэкапов:
- `createInboundBackup($inbound)` - создание бэкапа
- `restoreInboundBackup($backup, $targetServer)` - восстановление
- `cleanupOldBackups()` - очистка старых бэкапов

#### 2.8 NotificationService
Система уведомлений:
- `sendTelegramNotification($message, $level)` - уведомления в Telegram
- `notifyPoolCapacityReached($pool)` - уведомление о заполнении пула
- `notifyPaymentReceived($payment)` - уведомление об оплате

### Этап 3: События (Events)

#### 3.1 Пользовательские события
- `UserCreated` - создание пользователя
- `UserSubscriptionCreated` - создание подписки
- `UserSubscriptionRenewed` - продление подписки
- `UserSubscriptionExpired` - истечение подписки
- `UserDeleted` - удаление пользователя

#### 3.2 События оплат
- `OrderCreated` - создание заказа
- `PaymentReceived` - получение платежа
- `PaymentFailed` - неудачная оплата

#### 3.3 События серверов
- `ServerPoolCapacityReached` - достижение лимита пула
- `InboundBackupCreated` - создание бэкапа
- `ServerStatusUpdated` - обновление статуса сервера

### Этап 4: Слушатели событий (Listeners)

#### 4.1 Для пользовательских событий
- `CreateXuiClientsListener` - создание клиентов на XUI серверах
- `UpdateXuiClientsListener` - обновление клиентов на XUI серверах
- `DeleteXuiClientsListener` - удаление клиентов с XUI серверов
- `UpdateLeadtehVariablesListener` - обновление переменных в LeadTeh

#### 4.2 Для событий оплат
- `ProcessReferralRewardsListener` - обработка реферальных вознаграждений
- `SendPaymentNotificationListener` - отправка уведомлений об оплате

### Этап 5: Консольные команды

#### 5.1 Новые команды
- `subscription:check-expired` - проверка истекших подписок
- `subscription:process-renewals` - обработка продлений
- `backup:create-inbounds` - создание бэкапов инбаундов
- `backup:cleanup` - очистка старых бэкапов
- `pool:check-capacity` - проверка заполненности пулов
- `leadteh:sync-variables` - синхронизация переменных с LeadTeh

#### 5.2 Обновленные команды
- `subscriptions:sync` - теперь только синхронизация статистики
- `server:update-status` - обновление статуса серверов

### Этап 6: Jobs

#### 6.1 Новые Jobs
- `ProcessExpiredSubscriptionsJob` - обработка истекших подписок
- `CreateInboundBackupJob` - создание бэкапов
- `SyncTrafficStatisticsJob` - синхронизация статистики трафика
- `ProcessPaymentWebhookJob` - обработка вебхуков оплат
- `UpdateLeadtehVariablesJob` - обновление переменных LeadTeh

#### 6.2 Обновленные Jobs
- `SyncSubscriptionsJob` - теперь только статистика

### Этап 7: API контроллеры

#### 7.1 AdminApiController (с авторизацией)
- Управление пользователями
- Управление серверами и пулами
- Управление тарифными планами
- Просмотр статистики и бэкапов

#### 7.2 ExternalApiController (с API ключами)
- Создание пользователей
- Продление подписок
- Получение статистики пользователей
- Интеграция с внешними системами

#### 7.3 PaymentWebhookController
- Обработка вебхуков от T-Bank
- Обработка других платежных систем

### Этап 8: Админ-панель (React + Tailwind)

#### 8.1 Структура разделов
- **Dashboard** - общая статистика системы
- **Пользователи** - управление пользователями и подписками
- **Серверы** - управление серверами и пулами
- **Инбаунды** - управление инбаундами
- **Тарифы** - управление тарифными планами
- **Платежи** - просмотр заказов и платежей
- **Бэкапы** - управление бэкапами (только для суперадмина)
- **Настройки** - системные настройки

#### 8.2 Компоненты
- Таблицы с пагинацией и фильтрацией
- Формы создания/редактирования
- Графики статистики
- Модальные окна подтверждения
- Система уведомлений

### Этап 9: Миграция и очистка

#### 9.1 Миграция данных
- Создание демо тарифного плана
- Создание дефолтного пула серверов
- Перенос пользователей в новую структуру
- Создание подписок для существующих пользователей

#### 9.2 Очистка
- Удаление неиспользуемых файлов .md
- Удаление старых контроллеров и сервисов
- Удаление неиспользуемых миграций
- Очистка routes от старых маршрутов

## Порядок выполнения

1. **Создание новых миграций** (таблицы БД)
2. **Создание моделей** для новых таблиц
3. **Создание сервисов** (по одному за раз)
4. **Создание событий и слушателей**
5. **Обновление существующих контроллеров**
6. **Создание новых API контроллеров**
7. **Создание консольных команд и Jobs**
8. **Создание админ-панели**
9. **Миграция данных**
10. **Очистка старого кода**

## Дополнительные уточнения

### Логика работы с пулами серверов
1. При создании пользователя система ищет первый доступный пул (где количество пользователей < max_users)
2. Если все пулы заполнены, отправляется уведомление администратору
3. При истечении подписки пользователь удаляется из пула, освобождая место
4. При продлении подписки пользователь назначается в первый доступный пул

### Логика работы с тарифами
1. Демо-тариф назначается автоматически при создании пользователя
2. При переходе на /renew?uuid показывается выбор тарифов (если демо) или форма оплаты (если платный)
3. Цена рассчитывается с учетом скидок по сроку

### Система событий
Все критические операции (создание/продление/удаление пользователей) происходят через события для обеспечения атомарности и возможности отката.

### Интеграция с LeadTeh
- Обновление переменных происходит асинхронно через Jobs
- При ошибках интеграции основная логика не блокируется
- Реферальные вознаграждения начисляются только при успешной оплате

Готов начать реализацию. С какого этапа начнем?
