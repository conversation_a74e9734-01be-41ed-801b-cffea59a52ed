<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\ServerPool;
use App\Models\SubscriptionPlan;
use App\Models\XuiServer;

class InitialDataSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create default server pool
        $defaultPool = ServerPool::firstOrCreate(
            ['name' => 'Default Pool'],
            [
                'description' => 'Основной пул серверов',
                'max_users' => 250,
                'is_active' => true,
            ]
        );

        // Create demo subscription plan
        $demoplan = SubscriptionPlan::firstOrCreate(
            ['name' => 'Demo Plan', 'is_demo' => true],
            [
                'description' => 'Демо подписка на 1 день с безлимитным трафиком',
                'duration_amount' => 1,
                'duration_unit' => 'day',
                'traffic_gb' => null, // безлимит
                'server_count' => null, // все доступные
                'price_in_cents' => 0, // бесплатно
                'price_formatted' => 'Бесплатно',
                'discount_percent' => 0,
                'is_demo' => true,
                'is_active' => true,
                'is_archived' => false,
            ]
        );

        // Create some example paid plans
        $plans = [
            [
                'name' => '1 месяц - 100 ГБ',
                'description' => 'Подписка на 1 месяц с лимитом 100 ГБ',
                'duration_amount' => 1,
                'duration_unit' => 'month',
                'traffic_gb' => 100,
                'server_count' => null,
                'price_in_cents' => 50000, // 500 рублей
                'price_formatted' => '500 руб.',
                'discount_percent' => 0,
            ],
            [
                'name' => '3 месяца - 300 ГБ',
                'description' => 'Подписка на 3 месяца с лимитом 300 ГБ и скидкой 10%',
                'duration_amount' => 3,
                'duration_unit' => 'month',
                'traffic_gb' => 300,
                'server_count' => null,
                'price_in_cents' => 135000, // 1350 рублей (скидка 10%)
                'price_formatted' => '1350 руб.',
                'discount_percent' => 10,
            ],
            [
                'name' => '12 месяцев - Безлимит',
                'description' => 'Годовая подписка с безлимитным трафиком и скидкой 20%',
                'duration_amount' => 12,
                'duration_unit' => 'month',
                'traffic_gb' => null,
                'server_count' => null,
                'price_in_cents' => 480000, // 4800 рублей (скидка 20%)
                'price_formatted' => '4800 руб.',
                'discount_percent' => 20,
            ],
        ];

        foreach ($plans as $planData) {
            SubscriptionPlan::firstOrCreate(
                ['name' => $planData['name']],
                array_merge($planData, [
                    'is_demo' => false,
                    'is_active' => true,
                    'is_archived' => false,
                ])
            );
        }

        // Assign existing servers to default pool if any exist
        $existingServers = XuiServer::where('is_active', true)->get();
        foreach ($existingServers as $server) {
            $defaultPool->servers()->syncWithoutDetaching([$server->id]);
        }

        $this->command->info('Initial data seeded successfully!');
        $this->command->info("Created server pool: {$defaultPool->name}");
        $this->command->info("Created demo plan: {$demoplan->name}");
        $this->command->info("Created " . count($plans) . " paid plans");
        $this->command->info("Assigned {$existingServers->count()} existing servers to default pool");
    }
}
