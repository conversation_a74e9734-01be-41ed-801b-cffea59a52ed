<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('subscription_plans', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->text('description')->nullable();
            $table->integer('duration_amount'); // количество (1, 3, 12)
            $table->enum('duration_unit', ['day', 'month', 'year']); // единица измерения
            $table->integer('traffic_gb')->nullable(); // null = безлимит
            $table->integer('server_count')->nullable(); // null = все доступные
            $table->bigInteger('price_in_cents')->default(0)->comment('Сумма в minor units (копейки, центы и т.п.)'); // Цена в минимальных денежных единицах (копейках, центах и т.д.), 0 = бесплатно
            $table->string('price_formatted')->nullable()->default('0'); // цена как форматированная строка, 0 = бесплатно
            $table->decimal('discount_percent', 5, 2)->default(0); // скидка по сроку
            $table->boolean('is_demo')->default(false);
            $table->boolean('is_active')->default(true);
            $table->boolean('is_archived')->default(false);
            $table->timestamps();
            $table->softDeletes();

            // Indexes
            $table->index(['is_active', 'is_archived', 'deleted_at']);
            $table->index(['is_demo', 'deleted_at']);
            $table->index('name');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('subscription_plans');
    }
};
