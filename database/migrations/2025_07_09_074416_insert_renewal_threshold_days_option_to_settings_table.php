<?php

use App\Models\Setting;
use Illuminate\Database\Migrations\Migration;

return new class extends Migration
{
    private $key = 'renewal_threshold_days';
    private $value = 3;
    private $description = 'Number of days before the subscription expires. For example, before which the renewal button will be displayed';

    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Check if setting already exists
        if (! Setting::has($this->key)) {
            Setting::set($this->key, $this->value, $this->description);
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Setting::remove($this->key);
    }
};
