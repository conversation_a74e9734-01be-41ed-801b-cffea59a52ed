<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('payments', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('order_id');
            $table->enum('method', ['tbank', 'manual', 'balance'])->default('tbank');
            $table->enum('status', ['pending', 'processing', 'completed', 'failed', 'cancelled'])->default('pending');
            $table->string('external_id')->nullable()->comment('External payment system ID');
            $table->json('external_details')->nullable()->comment('External payment system response data');
            $table->string('payment_url')->nullable()->comment('Payment URL for user');
            $table->bigInteger('amount_in_cents');
            $table->timestamp('processed_at')->nullable();
            $table->timestamps();
            $table->softDeletes();

            // Non-cascading foreign keys
            $table->foreign('order_id')->references('id')->on('orders')->onDelete('restrict');

            // Indexes
            $table->index(['order_id', 'status', 'deleted_at']);
            $table->index(['method', 'status', 'deleted_at']);
            $table->index(['external_id', 'deleted_at']);
            $table->index(['status', 'processed_at', 'deleted_at']);
            $table->index(['created_at', 'deleted_at']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('payments');
    }
};
